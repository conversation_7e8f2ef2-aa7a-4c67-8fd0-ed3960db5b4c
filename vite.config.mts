import { UserConfigExport, ConfigEnv } from 'vite';
import vue from '@vitejs/plugin-vue'
import { viteVConsole } from 'vite-plugin-vconsole'
import vuetify from 'vite-plugin-vuetify'
import legacy from '@vitejs/plugin-legacy'
import {viteStaticCopy} from 'vite-plugin-static-copy'
import * as path from 'path'

// https://vitejs.dev/config/
export default ({ command, mode }: ConfigEnv): UserConfigExport => {
  return {
    base: "./",
    build: {
      target: ['es2015', 'chrome52'],
      minify: 'terser',
      assetsInlineLimit: 0,
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return 'vendor';
            }
          },
          entryFileNames: 'js/[name]-[hash].js',
          chunkFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            if (assetInfo?.name?.endsWith('.css')) {
              return 'css/[name]-[hash][extname]';
            }
            return 'assets/[name]-[hash][extname]';
          },
        }
      }
    },
    plugins: [
      vue(),
      legacy({
        targets: ['defaults', 'chrome 52', 'Android 4.1', 'iOS 7.1'],
        renderLegacyChunks: false,
        modernPolyfills: true,
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      }),
      viteVConsole({
        entry: path.resolve('src/main.ts'),
        enabled: command === 'build' && mode === 'development',
        plugin: [
          {
            id: 'my_plugin',
            name: 'Other',
            event: [
              {
                eventName: 'renderTab',
                callback: function (callback) {
                  callback('');
                }
              },
              {
                eventName: 'addTool',
                callback: function (callback) {
                  var button = {
                      name: 'Reload',
                      onClick: function() {
                        // @ts-ignore
                        (window as any).location.reload();
                      }
                  };
                  callback([button]);
                }
              }
            ]
          },
        ]
      }),
      vuetify({ autoImport: true }),
      viteStaticCopy({
        targets: [
          {
            src: 'static/*',
            dest: 'static/',
          },
        ],
      }),
    ],
    resolve: {
      alias: {
        "@": "/src",
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api']
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: 5556
    },
  }
}
