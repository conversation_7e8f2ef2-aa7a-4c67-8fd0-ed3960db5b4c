export interface ClientOptions {
  sdkAppId: number;
  userId: string;
  roomId: number;
  sdkSecretKey?: string;
  userSig?: string;
}
export type DeviceItem = Record<string, any>;

export enum DEVICETYPE {
  CAMERA = 2,
  MICROPHONE = 1,
}

export enum OPERATETYPE {
  OPENDEVICE = 1,
  CLOSEDEVICE = 2,
  ROOMSTATUSUPDATE = 3,
  STARTEXAM = 4,
  SUBMITEXAM = 5,
  DISSMISSBYADMIN = 6,
  WARNMASTER = 7,
}

export enum USERROLE {
  TEACHER = 1,
  STUDENT = 2,
  SUPERVISOR = 3,
}