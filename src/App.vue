<template>
  <router-view/>
</template>

<script lang="ts" setup>

</script>

<style lang="scss">
body {
  width: 100%;
  .com-verify-code {
    z-index: 2001;
  }
  .mark126 {
    z-index: 2002;
  }
  .com-verify-code .com-verify-conent .com-verify-ipt input {
    color: #333;
  }
  .trtc_autoplay_wrapper {
    color: #333;
  }
}

body {
  .v-input--density-compact {
    .v-field__input {
      min-height: 1.5rem;
      padding: 0 0.5rem;
    }
    .v-select__selection-text {
      font-size: 0.875rem;
    }
    .v-field--appended {
      padding-right: 0;
    }
    .v-select__menu-icon {
      margin-left: 0;
    }
  }
  .v-input--density-comfortable {
    .v-field__input {
      min-height: 1.75rem;
      padding: 0 0.5rem;
    }
    .v-select__selection-text {
      font-size: 0.875rem;
    }
    .v-field--appended {
      padding-right: 0;
    }
    .v-select__menu-icon {
      margin-left: 0;
    }
  }
  .v-input--density-default {
    .v-field__input {
      min-height: 2.25rem;
      padding: 0 0.5rem;
    }
  }
  .v-select__content {
    .v-list-item--one-line {
      min-height: 1.75rem;
    }
    .v-list-item-title {
      font-size: 0.875rem;
    }
  }
  .v-overlay {
    .v-overlay__scrim {
      background: rgb(0, 0, 0);
    }
  }
  .v-tab__slider {
    width: 1.75rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
</style>
