<template>
  <v-theme-provider theme="dark" with-background>
    <template v-if="isSupported">
      <template v-if="isMobile">
        <MainH5 v-if="isMucang || keepStay" />
        <MainWX v-else />
      </template>
      <MainPC v-else />
    </template>
  </v-theme-provider>
</template>

<script lang='ts' setup>
import TRTC from '@/services/trtc';
import TUIMessageBox from '@/components/TUI/MessageBox/index';
import MainH5 from '@/components/Main/MainH5.vue';
import MainWX from '@/components/Main/MainWX.vue';
import MainPC from '@/components/Main/MainPC.vue';
import { ref, getCurrentInstance } from 'vue';
import {MCProtocol} from '@simplex/simple-base'
import { isIOS, isMobile, isMucang } from '@/utils/utils';
import eventBus from '@/hooks/useMitt';

const { appContext } = getCurrentInstance()!
let isSupported = ref(false)
let keepStay = ref(false)

eventBus.on('send-outside-keep-stay', () => keepStay.value = true);

// check current environment is supported TRTC or not
TRTC.isSupported().then((checkResult) => {
  const {isBrowserSupported, isWebRTCSupported, isMediaDevicesSupported, isH264DecodeSupported, isVp8DecodeSupported} = checkResult.detail
  const result = checkResult.result && isBrowserSupported && isWebRTCSupported && isMediaDevicesSupported && isH264DecodeSupported && isVp8DecodeSupported
  console.log('checkResult', checkResult.result);
  console.log('checkDetail', checkResult.detail);
  console.log('isSupported', result);
  isSupported.value = result
  if (!result) {
    let version = ''
    if (isIOS) {
      version = 'IOS 14.3'
    } else {
      version = '安卓 11'
    }
    TUIMessageBox({
      title: '提示',
      message: `手机系统版本过低，无法使用会议室功能，请将手机系统升级至${version}以上`,
      confirmButtonText: '确定',
      callback: async (result) => {
        if (result) {
          MCProtocol.Core.Web.close()
        }
      },
      closeInMask: false,
      appContext
    });
  }
});

</script>
