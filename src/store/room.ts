import { defineStore } from 'pinia';
import TRTC from '@/services/trtc';
import {TRTCStreamType} from 'trtc-sdk-v5'
import useBasicStore from './basic';
import {USERROLE} from '@/types/type';
import {SAU_LISTENER} from '@/utils/constant'

export type StreamInfo = {
  userId: string,
  userName?: string,
  avatarUrl?: string,
  hasAudioStream?: boolean,
  hasVideoStream?: boolean,
  hasScreenStream?: boolean,
  streamType: TRTCStreamType,
  isVisible: boolean,
}

export type UserInfo = {
  userId: string,
  userName: string,
  avatarUrl: string,
  hasAudioStream: boolean,
  hasVideoStream: boolean,
  hasScreenStream: boolean,
  isVideoVisible: boolean,
  isScreenVisible: boolean,
  isMessageDisabled: boolean,
  userRole: USERROLE,
  cameraStreamInfo: {
    userId: string,
    userName: string,
    avatarUrl: string,
    hasAudioStream: boolean,
    hasVideoStream: boolean,
    streamType: TRTCStreamType,
    isVisible: boolean,
  },
  screenStreamInfo: {
    userId: string,
    userName: string,
    avatarUrl: string,
    hasScreenStream: boolean,
    streamType: TRTCStreamType,
    isVisible: boolean,
  },
}

export function set(target: any, key: any, val: any) {
  if (Array.isArray(target)) {
    target.splice(key, 1, val);
    return val;
  }
  // eslint-disable-next-line no-param-reassign
  target[key] = val;
  return val;
}

export function del(target: any, key: any) {
  if (Array.isArray(target) && typeof key === 'number') {
    target.splice(key, 1);
    return;
  }
  // eslint-disable-next-line no-param-reassign
  delete target[key];
}


const useRoomStore = defineStore('room', {
  state: () => ({
    isMaster: false,
    isSauListener: false,
    isTrtcReady: false,
    status: 0,
    currentCameraId: null,
    currentMicrophoneId: null,
    currentSpeakerId: null,
    cameraList: [] as MediaDeviceInfo[],
    microphoneList: [] as MediaDeviceInfo[],
    speakerList: [] as MediaDeviceInfo[],
    localUser: {
      userId: '',
      userName: '',
      avatarUrl: '',
      hasAudioStream: false,
      hasVideoStream: false,
      hasScreenStream: false,
      userRole: USERROLE.STUDENT,
      isMessageDisabled: false,
      cameraStreamInfo: {
        userId: '',
        userName: '',
        avatarUrl: '',
        hasAudioStream: false,
        hasVideoStream: false,
        streamType: TRTC.TYPE.STREAM_TYPE_MAIN,
        isVisible: true,
      },
      screenStreamInfo: {
        userId: '',
        userName: '',
        avatarUrl: '',
        hasScreenStream: false,
        streamType: TRTC.TYPE.STREAM_TYPE_SUB,
        isVisible: true,
      },
    } as UserInfo,
    remoteUserObj: {} as {
      [key: string]: UserInfo;
    },
    userVolumeObj: {},
  }),
  getters: {
    localStream(state) {
      const { userId, userName, avatarUrl, hasAudioStream, hasVideoStream } = state.localUser;
      Object.assign(
        state.localUser.cameraStreamInfo,
        { userId, userName, avatarUrl, hasAudioStream, hasVideoStream },
      );
      return state.localUser.cameraStreamInfo;
    },
    localScreenStream(state) {
      const { userId, userName, avatarUrl, hasScreenStream } = state.localUser;
      Object.assign(
        state.localUser.screenStreamInfo,
        { userId, userName, avatarUrl, hasScreenStream },
      );
      return state.localUser.screenStreamInfo;
    },
    remoteStreamObj(state) {
      const obj: Record<string, StreamInfo> = {};
      Object.values(state.remoteUserObj).forEach((userInfo) => {
        const {
          userId,
          avatarUrl,
          userName,
          hasAudioStream,
          hasVideoStream,
          hasScreenStream,
          isVideoVisible,
          isScreenVisible,
        } = userInfo;
        obj[`${userId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`] = Object.assign(userInfo.cameraStreamInfo, { userId, avatarUrl, userName, hasAudioStream, hasVideoStream, streamType: TRTC.TYPE.STREAM_TYPE_MAIN, isVisible: isVideoVisible });
        if (hasScreenStream) {
          obj[`${userId}_${TRTC.TYPE.STREAM_TYPE_SUB}`] = Object.assign(userInfo.screenStreamInfo, { userId, avatarUrl, userName, hasScreenStream, streamType: TRTC.TYPE.STREAM_TYPE_SUB, isVisible: isScreenVisible });
        }
      });
      return obj;
    },
    remoteStreamList(): Array<StreamInfo> {
      return Object.values(this.remoteStreamObj).filter(item => !item.userId.match('wbpush_'));
    },
    streamList(): Array<StreamInfo> {
      const list = [this.localStream, ...this.remoteStreamList].filter(item => item.userId !== SAU_LISTENER)
      if (this.localUser.hasScreenStream) {
        list.unshift(this.localScreenStream);
      }
      return list;
    },
    streamNumber() {
      return this.streamList.length;
    },
    remoteUserList(state): Array<UserInfo> {
      return Object.values(state.remoteUserObj).filter(item => !item.userId.match('wbpush_'))
    },
    userList(state): Array<UserInfo> {
      return [state.localUser, ...this.remoteUserList].filter(item => item.userId !== SAU_LISTENER)
    },
    userNumber() {
      return this.userList.length;
    },
    whStream(): StreamInfo {
      return Object.values(this.remoteStreamObj).filter(item => item.userId.match('wbpush_'))[0];
    }
  },
  actions: {
    setRoomStatus(status: number) {
      this.status = status
    },
    setLocalUser(obj: Record<string, any>) {
      Object.assign(this.localUser, obj);
    },
    updateUserInfo(userId: string, userInfo: object) {
      if (userId === this.localUser.userId) {
        Object.assign(this.localUser, userInfo);
      } else {
        if (this.remoteUserObj[userId]) {
          Object.assign(this.remoteUserObj[userId], userInfo);
        }
        // else {
        //   const newUserInfo = Object.assign(this.getNewUserInfo(userId), userInfo);
        //   set(this.remoteUserObj, userId, newUserInfo);
        // }
      }
    },
    getNewUserInfo(userId: string): UserInfo {
      const newUserInfo = {
        userId,
        userName: '',
        avatarUrl: '',
        hasAudioStream: false,
        hasVideoStream: false,
        hasScreenStream: false,
        isVideoVisible: true,
        isScreenVisible: true,
        isMessageDisabled: false,
        userRole: USERROLE.STUDENT,
        cameraStreamInfo: {
          userId,
          userName: '',
          avatarUrl: '',
          hasAudioStream: false,
          hasVideoStream: false,
          streamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          isVisible: true,
        },
        screenStreamInfo: {
          userId,
          userName: '',
          avatarUrl: '',
          hasScreenStream: false,
          streamType: TRTC.TYPE.STREAM_TYPE_SUB,
          isVisible: true,
        },
      };
      return newUserInfo;
    },
    addRemoteUser(userInfo: {userId: string, userRole: USERROLE}) {
      const { userId } = userInfo;
      const basicStore = useBasicStore();
      if (!userId || userId === basicStore.userId) {
        return;
      }
      if (this.remoteUserObj[userId]) {
        Object.assign(this.remoteUserObj[userId], userInfo);
      } else {
        const newUserInfo = Object.assign(this.getNewUserInfo(userId), userInfo);
        set(this.remoteUserObj, userId, newUserInfo);
      }
    },
    removeRemoteUser(userId: string) {
      const basicStore = useBasicStore();
      if (!userId || userId === basicStore.userId) {
        return;
      }
      del(this.remoteUserObj, userId);
    },
    updateUserVideoState(userId: string, streamType: string, hasVideo: boolean) {
      const basicStore = useBasicStore();
      let user = userId === basicStore.userId ? this.localUser : this.remoteUserObj[userId];
      // You need to determine whether hasVideo is true or not to avoid the video cancellation event being thrown after onRemoteUserLeaveRoom
      if (!user && hasVideo) {
        user = this.getNewUserInfo(userId);
        set(this.remoteUserObj, userId, user);
      }
      if (user) {
        if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
          user.hasVideoStream = hasVideo;
        } else if (streamType === TRTC.TYPE.STREAM_TYPE_SUB) {
          user.hasScreenStream = hasVideo;
        }
      }
    },
    updateUserAudioState(userId: string, hasAudio: boolean) {
      const basicStore = useBasicStore();
      let user = userId === basicStore.userId ? this.localUser : this.remoteUserObj[userId];
      // You need to determine if hasAudio is true to avoid audio cancellation events being thrown after onRemoteUserLeaveRoom
      if (!user && hasAudio) {
        user = this.getNewUserInfo(userId);
        set(this.remoteUserObj, userId, user);
      }
      if (user) {
        user.hasAudioStream = hasAudio;
      }
    },
    setAudioVolume(audioVolumeArray: []) {
      const basicStore = useBasicStore();
      audioVolumeArray.forEach((audioVolumeItem: any) => {
        let { userId, volume } = audioVolumeItem;
        if (userId === '') {
          userId = basicStore.userId;
        }
        set(this.userVolumeObj, userId, volume);
      });
    },
    setCurrentCameraId(deviceId: string) {
      this.currentCameraId = deviceId;
    },
    setCurrentMicrophoneId(deviceId: string) {
      this.currentMicrophoneId = deviceId;
    },
    setCurrentSpeakerId(deviceId: string) {
      this.currentSpeakerId = deviceId;
    },
    setCameraList(deviceList: MediaDeviceInfo[]) {
      this.cameraList = deviceList;
      if (!this.currentCameraId && deviceList.length > 0) {
        this.setCurrentCameraId(deviceList[0].deviceId);
      }
    },
    setMicrophoneList(deviceList: MediaDeviceInfo[]) {
      this.microphoneList = deviceList;
      if (!this.currentMicrophoneId && deviceList.length > 0) {
        if (this.localUser.userId !== SAU_LISTENER) {
          this.setCurrentMicrophoneId(deviceList[0].deviceId);
        }
      }
    },
    setSpeakerList(deviceList: MediaDeviceInfo[]) {
      this.speakerList = deviceList;
      if (!this.currentSpeakerId && deviceList.length > 0) {
        this.setCurrentSpeakerId(deviceList[0].deviceId);
      }
    },
    setMuteUserChat(userId: string, muted: boolean) {
      if (userId === this.localUser.userId) {
        this.localUser.isMessageDisabled = muted
      } else {
        const remoteUserInfo = this.remoteUserObj[userId];
        if (remoteUserInfo) {
          remoteUserInfo.isMessageDisabled = muted;
        }
      }
    },
    setTrtcReady(isTrtcReady: boolean) {
      this.isTrtcReady = isTrtcReady;
    },
  },
});

export default useRoomStore;
