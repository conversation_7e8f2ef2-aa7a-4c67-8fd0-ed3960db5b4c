import { defineStore } from 'pinia';

interface MessageItem {
  ID: string;
  type: string;
  payload: {
    text: string;
  };
  avatar: string,
  nick: string;
  from: string;
  flow: string;
  sequence: number;
}

interface ChatState {
  isTimReady: boolean;
  isSdkReady: boolean;
  messageList: MessageItem[];
  isMessageDisabled: boolean;
  unReadCount: number;
  isCompleted: boolean;
  // Is the list of all messages pulled
  nextReqMessageId: string;
}

const useChatStore = defineStore('chat', {
  state: (): ChatState => ({
    isTimReady: false,
    isSdkReady: false,
    messageList: [],
    isMessageDisabled: false,
    unReadCount: 0,
    isCompleted: false,
    nextReqMessageId: '',
  }),
  actions: {
    updateMessageList(message: MessageItem) {
      const messageIds = this.messageList.map(message => message.ID);
      if (messageIds.indexOf(message.ID) === -1) {
        this.messageList = this.messageList.concat([message]);
      }
    },
    setMessageListInfo(messageList:MessageItem[], isCompleted: boolean, nextReqMessageId: string) {
      this.messageList = messageList;
      this.isCompleted = isCompleted;
      this.nextReqMessageId = nextReqMessageId;
    },
    updateUnReadCount(count: number) {
      this.unReadCount = count;
    },
    setSendMessageDisableChanged(isDisable: boolean) {
      this.isMessageDisabled = isDisable;
    },
    setTimReady(isTimReady: boolean) {
      this.isTimReady = isTimReady;
    },
    setSdkReady(isSdkReady: boolean) {
      this.isSdkReady = isSdkReady;
    },
  },
});

export default useChatStore;
