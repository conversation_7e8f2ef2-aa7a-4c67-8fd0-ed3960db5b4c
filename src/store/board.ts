import { defineStore } from 'pinia';

const useBoardStore = defineStore('board', {
  state: () => ({
    isSdkReady: false,
    isSignalReady: false,
    isTiwReady: false, // 白板是否已经Ready
    whUserId: '',
    whUserSig: '',
    current: {
      toolType: 0,
      fileInfo: {},
      boardSetting: {},
    },
    boardSetting: {},
    rightBarShow: false,
  }),
  actions: {
    setSdkReady(isSdkReady: boolean) {
      this.isSdkReady = isSdkReady;
    },
    setSignalReady(isSignalReady: boolean) {
      this.isSignalReady = isSignalReady;
    },
    setTiwReady(isTiwReady: boolean) {
      this.isTiwReady = isTiwReady;
    },
    setCurrentFile(fileInfo: object) {
      this.current.fileInfo = fileInfo;
    },
    updateBoardSetting(setting: object) {
      Object.assign(this.current.boardSetting, setting);
    },
  }
});

export default useBoardStore;
