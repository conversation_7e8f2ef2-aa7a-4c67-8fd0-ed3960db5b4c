<template>
  <iframe ref="iframeRef" width="100%" height="100%" class="border-0" :src="props.src"></iframe>
</template>

<script lang='ts' setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

interface Props {
  src: string,
}

const iframeRef = ref<HTMLIFrameElement|null>(null)
const props = defineProps<Props>();
const emit = defineEmits(['onMessage', 'exitRoom']);

function handleMessage(e: MessageEvent) {
  let iframe = iframeRef.value
  var iframeWindow = iframe?.contentWindow;
  if (e.source === iframeWindow) {
    emit('onMessage', e)
  }
}

onMounted(() => {
  window.addEventListener('message', handleMessage)
})
onBeforeUnmount(() => {
  window.removeEventListener('message', handleMessage)
})

</script>
