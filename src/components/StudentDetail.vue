<template>
  <v-tabs
    v-model="tab" fixed-tabs :show-arrows="false" class="flex-shrink-0" color="mc-green-lighten-1"
  >
    <v-tab value="base" class="px-2" style="min-width: auto;">基础信息</v-tab>
    <v-tab value="wrong" class="px-2" style="min-width: auto;">错题本</v-tab>
    <v-tab value="recentExam" class="px-2" style="min-width: auto;">近十场考试</v-tab>
    <v-tab value="mockExam" class="px-2" style="min-width: auto;">模拟考记录</v-tab>
  </v-tabs>
  <v-tabs-window v-model="tab" class="flex-grow-1 tab-scroll">
    <v-tabs-window-item class="pa-4 overflow-y-scroll" value="base">
      <v-card class="mb-4 bg-mc-bule-darken-2">
        <v-card-title class="text-subtitle-1 mc-bline">
          <span>
            <i>个人资料</i>
          </span>
        </v-card-title>
        <v-card-text v-if="studentInfo.sno">
          <v-row no-gutters class="py-2">
            <v-col>学员学历：{{studentProfile.educationName}}</v-col>
            <v-col>约考时间：<template v-if="studentInfo.studySummary.reserveExamTime">{{formatDate(studentInfo.studySummary.reserveExamTime, 'yyyy-MM-dd')}}</template><template v-else>暂无预约</template></v-col>
          </v-row>
          <v-row no-gutters class="py-2">
            <v-col>学员年龄：{{studentProfile.age || '未知'}}</v-col>
            <v-col>模考次数：{{(studentInfo.studySummary.totalMockExamCount ||  studentInfo.studySummary.totalMockExamCount === 0 )?studentInfo.studySummary.totalMockExamCount : '--'}}</v-col>
          </v-row>
          <v-row no-gutters class="py-2">
            <v-col>考试城市：{{studentProfile.cityName}} <v-chip v-if="studentProfile.existLocalTiku" size="x-small" class="rounded" color="mc-red-darken-1 px-1" variant="elevated">含地方题</v-chip></v-col>
          </v-row>
          <v-row no-gutters class="py-2">
            <v-col>累计做题数：{{(studentInfo.studySummary.totalExercisesCount ||  studentInfo.studySummary.totalExercisesCount === 0)?studentInfo.studySummary.totalExercisesCount : '--'}}</v-col>
            <v-col>近五次平均分：{{(studentInfo.studySummary.mockExamAvgScore ||  studentInfo.studySummary.mockExamAvgScore === 0)?studentInfo.studySummary.mockExamAvgScore : '--'}}</v-col>
          </v-row>
          <v-row no-gutters class="py-2">
            <v-col>辅导科目：{{studentProfile.kemuName}}</v-col>
            <v-col>预测通过率：{{studentInfo.studySummary.passRatePrediction || 0}}%</v-col>
          </v-row>
        </v-card-text>
      </v-card>
      
      <v-card class="mb-4 bg-mc-bule-darken-2">
        <v-card-title class="text-subtitle-1 mc-bline">
          <span>
            <i>学员评语</i>
          </span>
        </v-card-title>
        <template v-for="(item, index) in commentList" :key="item.commentTime">
          <v-card-text>
            <v-col class="pa-0">{{item.content}}</v-col>
            <v-row no-gutters class="opacity-60 pt-4">
              <v-col>{{ item.commentUser }}</v-col>
              <v-col>{{ formatDate(item.commentTime, 'yyyy-MM-dd hh:mm:ss') }}</v-col>
            </v-row>
          </v-card-text>
          <v-divider v-if="index+1 !== commentList.length" class="mx-4 border-dashed" color="#000"></v-divider>
        </template>
      </v-card>
    </v-tabs-window-item>
    <v-tabs-window-item class="pa-4 overflow-y-scroll" value="wrong">
      <v-row no-gutters class="mr-n3">
        <v-col v-for="item in studentInfo?.wrongBook?.wrongQuestions" :key="item.category" @click="toWrongList({wrongListType: WrongListType.WrongQuestions, categoryId: item.categoryId, name: item.category}, item.questionList.length)" cols="6">
          <v-hover>
            <template v-slot:default="{ isHovering, props }">
              <v-sheet class="pa-3 rounded mr-3 mt-3 text-truncate text-subtitle-2 cursor-pointer" :class="isHovering ? 'bg-mc-bule-darken-5' : 'bg-mc-bule-darken-4'" v-bind="props">
                <span>{{item.category}}</span>
                <span v-if="item.type === 'all'">({{item.questionList.length}})</span>
                <span v-else>({{item.questionList.length}}/{{item.allCount}})</span>
              </v-sheet>
            </template>
          </v-hover>
        </v-col>
      </v-row>
    </v-tabs-window-item>
    <v-tabs-window-item class="pa-4 overflow-y-scroll" value="recentExam">
      <v-row no-gutters class="mr-n3">
        <v-col v-for="item in studentInfo?.wrongBook?.lastExamWrongQuestions" :key="item.category" @click="toWrongList({wrongListType: WrongListType.LastExamWrongQuestions, categoryId: item.categoryId, name: item.category}, item.questionList.length)" cols="6">
          <v-hover>
            <template v-slot:default="{ isHovering, props }">
              <v-sheet class="pa-3 rounded mr-3 mt-3 text-truncate text-subtitle-2 cursor-pointer" :class="isHovering ? 'bg-mc-bule-darken-5' : 'bg-mc-bule-darken-4'" v-bind="props">
                <span>{{item.category}}</span>
                <span v-if="item.type === 'all'">({{item.questionList.length}})</span>
                <span v-else>({{item.questionList.length}}/{{item.allCount}})</span>
              </v-sheet>
            </template>
          </v-hover>
        </v-col>
      </v-row>
    </v-tabs-window-item>
    <v-tabs-window-item class="pa-4 h-100" value="mockExam">
      <div class="d-flex flex-column h-100">
        <v-table density="compact" class="bg-transparent flex-fill h-0" fixed-header height="100%">
            <thead>
              <tr>
                <th style="background-color: #0F1F47; height: 32px;" class="text-left elevation-0 pl-4 pr-2">
                  模考时间
                </th>
                <th style="background-color: #0F1F47; height: 32px;" class="text-left elevation-0 py-2 px-1">
                  耗时
                </th>
                <th style="background-color: #0F1F47; height: 32px;" class="text-left elevation-0 py-2 px-1">
                  得分
                </th>
                <th style="background-color: #0F1F47; height: 32px;" class="text-center elevation-0 pl-1 pr-3">
                  错题
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in examList" :key="item.examTime">
                <td class="border-0 py-2 pl-4 pr-2">{{ formatDate(item.examTime, 'yyyy-MM-dd hh:mm:ss') }}</td>
                <td class="border-0 py-2 px-1">{{ formatTimeStr(item.durationMillis, 'mm:ss') }}</td>
                <td class="border-0 py-2 px-1">{{ item.score }}</td>
                <td class="border-0 py-2 pl-1 pr-3">
                  <v-btn class="bg-mc-bule-lighten-1" width="68" block size="small" @click="toWrongList({wrongListType: WrongListType.Exam, uniqueId: item.uniqueId, name: '考试错题'}, item.errorqIdList.length)">
                    查看
                  </v-btn>
                </td>
              </tr>
            </tbody>
          </v-table>
          <v-btn class="bg-mc-bule-lighten-1 rounded-pill mt-2 mx-2"  variant="text" @click="examTotal">
            在白板中打开
          </v-btn>
      </div>
    </v-tabs-window-item>
  </v-tabs-window>
</template>

<script lang='ts' setup>
import { onMounted, ref, getCurrentInstance, watch } from 'vue';
import useBasicStore from '@/store/basic';
import { httpRequest, URLParams, formatDate, formatTimeStr, getAuthToken, convertParamsStr, getHost, trackEvent } from '@/utils/utils';
import TUIMessage from '@/components/TUI/Message/index';
import eventBus from '@/hooks/useMitt';
import {WrongListType, WrongListWrongQuestions, WrongListExam} from '@/utils/constant';

interface Props {
  kemuParams: {
    kemu: number,
    sceneCode: number,
  },
  tutorKemu: number,
  carType: string,
}
const props = defineProps<Props>();
const { appContext } = getCurrentInstance()!
const emit = defineEmits(['closeSelf']);
const basicStore = useBasicStore();
let tab = ref(null)

interface StudentProfileItem {
  educationName: string
  age: string
  cityName: string
  kemuName: string
  existLocalTiku: boolean
}
interface QusestionItem {
  category: string
  categoryId: number
  questionList: []
  type: string
  allCount: number
}
interface StudentInfoItem {
  sno: string,
  studySummary: {
    reserveExamTime: number,
    totalMockExamCount: number,
    totalExercisesCount: number,
    mockExamAvgScore: number,
    passRatePrediction: number
  },
  wrongBook: {
    wrongQuestions:  QusestionItem[],
    lastExamWrongQuestions:  QusestionItem[],
  }
}

const studentInfo = ref({} as StudentInfoItem)
const studentProfile = ref({} as StudentProfileItem)
const commentList = ref<{
  commentTime: number,
  content: string,
  commentUser: string,
}[]>([])
const examList = ref<{
  examTime: number,
  durationMillis: number,
  score: number,
  uniqueId: string,
  errorqIdList: [],
}[]>([])

async function getWrongListUrl(params: WrongListWrongQuestions | WrongListExam) {
  const host = await getHost()
  const authToken = await getAuthToken()
  const courseId = URLParams.courseId
  return `${host}personal-training-management/?door=${authToken}#/${courseId}/main/wrongList?${convertParamsStr(params)}`
}

async function getExamTotalUrl(params: {sno: string, carType: string, tutorKemu: number}) {
  const host = await getHost()
  const authToken = await getAuthToken()
  const courseId = URLParams.courseId
  return `${host}personal-training-management/?door=${authToken}#/${courseId}/main/examTotal?${convertParamsStr(params)}`
}

onMounted(async () => {
  getStudentSummary().then(res => {
    studentInfo.value = res
  })
  getStudentProfile().then(res => {
    studentProfile.value = res
  })
  getStudentCommentList().then(res => {
    commentList.value = res?.itemList || res
  })
  getMockAllList().then(res => {
    examList.value = res?.itemList || res
  })
})

watch(() => [props.kemuParams, props.carType], () => {
  getStudentSummary().then(res => {
    studentInfo.value = res
  })
  getMockAllList().then(res => {
    examList.value = res?.itemList || res
  })
}, {
  deep: true
})

function openTeachPlan(url: string) {
  if (!url) return
  eventBus.emit('tiw-add-element', url);
  emit('closeSelf')
}

async function toWrongList(
  params: {wrongListType: WrongListType.Exam | WrongListType.WrongQuestions | WrongListType.LastExamWrongQuestions, categoryId?: number, uniqueId?: string, name: string},
  length: number
) {
  if (length === 0) {
    TUIMessage({
      type: 'warning',
      message: '没有错题，请重新选择',
      duration: 3000,
      appContext
    })
  }
  let url = ''
  if (params.wrongListType === WrongListType.Exam) {
    url = await getWrongListUrl({
      wrongListType: params.wrongListType,
      uniqueId: params.uniqueId!,
      name: params.name,
      carType: props.carType,
      tutorKemu: props.tutorKemu,
      sno: basicStore.sno,
    })
  } else if (params.wrongListType === WrongListType.WrongQuestions || WrongListType.LastExamWrongQuestions) {
    url = await getWrongListUrl({
      wrongListType: params.wrongListType,
      categoryId: params.categoryId!,
      name: params.name,
      carType: props.carType,
      tutorKemu: props.tutorKemu,
      sno: basicStore.sno,
    })
  }

  openTeachPlan(url)

  trackEvent({
    fragmentName1: '学员档案',
    actionType: '点击',
    actionName: '打开试题列表',
    url,
  })
}

async function examTotal() {
  const url = await getExamTotalUrl({
    sno: basicStore.sno,
    carType: props.carType,
    tutorKemu: props.tutorKemu,
  })
  openTeachPlan(url)

  trackEvent({
    fragmentName1: '学员档案',
    actionType: '点击',
    actionName: '打开模拟考试总览',
    url,
  })
}


async function getStudentSummary() {
  return await httpRequest({
    url: 'api/web/student/get-student-summary.htm',
    params: {
      sno: basicStore.sno,
      carType: props.carType,
      ...props.kemuParams
    },
    host: 'parrot',
  })
}

async function getStudentProfile() {
  return await httpRequest({
    url: 'api/web/student/get-student-profile.htm',
    params: {
      sno: basicStore.sno,
    },
    host: 'parrot',
  })
}

async function getStudentCommentList() {
  return await httpRequest({
    url: 'api/web/comment/student-comment-list.htm',
    params: {
      sno: basicStore.sno,
    },
    host: 'parrot',
  })
}

async function getMockAllList() {
  return await httpRequest({
    url: 'api/web/student/list-all-mock-exam-record.htm',
    params: {
      sno: basicStore.sno,
      carType: props.carType,
      ...props.kemuParams,
      page: 1,
      limit: 15,
    },
    host: 'parrot',
  })
}

</script>

<style lang="scss">
.tab-scroll {
  .v-window__container {
    height: 100% !important;
  }
}
.mc-bline {
  span {
    i {
      position: relative;
      z-index: 1;
      font-style: normal;
    }
    position: relative;
      &::after{
      content: '';
      position: absolute;
      width: 100%;
      height: 10px;
      left: 0;
      bottom: -3px;
      background-color: #0a4282;
    }
  }
}
</style>