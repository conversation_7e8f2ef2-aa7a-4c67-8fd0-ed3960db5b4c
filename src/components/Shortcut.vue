<template>
  <v-card class="bg-mc-bule-darken-7">
    <v-card-title>快捷键设置</v-card-title>
    <v-card-text class="px-4 py-2">
      鼠标模式
      <v-toolbar density="compact" class="bg-transparent pa-0">
        <v-text-field v-model="mouse" @keydown="keydown($event, 'mouse')" density="default" bg-color="mc-bule-darken-4" hide-details variant="solo" flat></v-text-field>
        <v-btn icon="mdi-trash-can-outline" class="ml-2 pa-2" size="x-small" variant="plain" rounded="sm" @click="setKey('mouse', '')"></v-btn>
      </v-toolbar>
    </v-card-text>
    <v-card-text class="px-4 py-2">
      画笔模式
      <v-toolbar density="compact" class="bg-transparent pa-0">
        <v-text-field v-model="brush" @keydown="keydown($event, 'brush')" density="default" bg-color="mc-bule-darken-4" hide-details variant="solo" flat></v-text-field>
        <v-btn icon="mdi-trash-can-outline" class="ml-2 pa-2" size="x-small" variant="plain" rounded="sm" @click="setKey('brush', '')"></v-btn>
      </v-toolbar>
    </v-card-text>
    <v-card-text class="px-4 py-2">
      图形模式
      <v-toolbar density="compact" class="bg-transparent pa-0">
        <v-text-field v-model="shape" @keydown="keydown($event, 'shape')" density="default" bg-color="mc-bule-darken-4" hide-details variant="solo" flat></v-text-field>
        <v-btn icon="mdi-trash-can-outline" class="ml-2 pa-2" size="x-small" variant="plain" rounded="sm" @click="setKey('shape', '')"></v-btn>
      </v-toolbar>
    </v-card-text>
    <v-card-text class="px-4 py-2">
      文本模式
      <v-toolbar density="compact" class="bg-transparent pa-0">
        <v-text-field v-model="text" @keydown="keydown($event, 'text')" density="default" bg-color="mc-bule-darken-4" hide-details variant="solo" flat></v-text-field>
        <v-btn icon="mdi-trash-can-outline" class="ml-2 pa-2" size="x-small" variant="plain" rounded="sm" @click="setKey('text', '')"></v-btn>
      </v-toolbar>
    </v-card-text>
    <v-card-text class="px-4 py-2">
      <div>清空白板</div>
      <v-toolbar density="compact" class="bg-transparent pa-0">
        <v-text-field v-model="clearBoard" @keydown="keydown($event, 'clearBoard')" density="default" bg-color="mc-bule-darken-4" hide-details variant="solo" flat></v-text-field>
        <v-btn icon="mdi-trash-can-outline" class="ml-2 pa-2" size="x-small" variant="plain" rounded="sm" @click="setKey('clearBoard', '')"></v-btn>
      </v-toolbar>
    </v-card-text>
    <v-card-actions class="pa-4">
      <v-btn @click="emit('closeSelf')">取消</v-btn>
      <v-btn class="bg-mc-bule-lighten-1" @click="save">保存</v-btn>
    </v-card-actions>
  </v-card>
</template>

<script lang='ts' setup>
import { ref, onMounted, getCurrentInstance, ComponentInternalInstance } from 'vue';
import TUIMessage from '@/components/TUI/Message/index';
import {isMacOs} from '@/utils/utils';

const { appContext } = getCurrentInstance() as ComponentInternalInstance
const localKey = 'shortcut-setting'

let mouse = ref('')
let brush = ref('')
let shape = ref('')
let text = ref('')
let clearBoard = ref('')

function capitalizeFirstLetter(str: string): string {
  if (!str) return '';
  return str.charAt(0).toLocaleUpperCase() + str.slice(1);
}
function getKeyComp(event: KeyboardEvent): string {
  let keyList = []
  if (event.ctrlKey) {
    keyList.push('Ctrl')
  }
  if (event.metaKey && isMacOs) {
    keyList.push('Command')
  }
  if (event.shiftKey) {
    keyList.push('Shift')
  }
  if (event.altKey) {
    keyList.push('Alt')
  }
  if (event.key && !['Control', 'Meta', 'Shift', 'Alt'].includes(event.key)) {
    keyList.push(capitalizeFirstLetter(event.key))
  }
  return keyList.join(' + ')
}

function setKey(model: string, value: string) {
  switch (model) {
    case 'mouse':
      mouse.value = value
      break
    case 'brush':
      brush.value = value
      break
    case 'shape':
      shape.value = value
      break
    case 'text':
      text.value = value
      break
    case 'clearBoard':
      clearBoard.value = value
      break
  }
}

onMounted(() => {
  let setting = JSON.parse(localStorage.getItem(localKey) || '{}');
  mouse.value = setting.mouse || ''
  brush.value = setting.brush || ''
  shape.value = setting.shape || ''
  text.value = setting.text || ''
  clearBoard.value = setting.clearBoard || ''
});

function save() {
  const setting = {
    mouse: mouse.value,
    brush: brush.value,
    shape: shape.value,
    text: text.value,
    clearBoard: clearBoard.value,
  }as {[key: string]: any;}
  const keys = Object.keys(setting).filter(key => setting[key]).map(key => setting[key]);
  const invaild = keys.filter(key => key.indexOf(' + ') === -1);
  if (invaild.length > 0) {
    TUIMessage({
      type: 'error',
      message: `请以组合形式设置快捷键，如：Ctrl + A`,
      duration: 2000,
      appContext
    });
    return;
  }
  const duplicates = keys.filter((key, index) => keys.indexOf(key) !== index);
  if (duplicates.length > 0) {
    TUIMessage({
      type: 'error',
      message: `快捷键设置存在重复: ${duplicates.join(', ')}`,
      duration: 2000,
      appContext
    });
    return;
  }
  localStorage.setItem(
    localKey,
    JSON.stringify({
      mouse: mouse.value,
      brush: brush.value,
      shape: shape.value,
      text: text.value,
      clearBoard: clearBoard.value,
    })
  );
  emit('onSave')
  emit('closeSelf')
}

function keydown(event: KeyboardEvent, model: string) {
  if (event.key === 'Process') {
    event.preventDefault()
    TUIMessage({
      type: 'error',
      message: '中文输入法不支持快捷键设置',
      duration: 2000,
      appContext
    })
    return
  }

  event.preventDefault()
  setKey(model, getKeyComp(event))
}

const emit = defineEmits(['closeSelf', 'onSave']);

</script>