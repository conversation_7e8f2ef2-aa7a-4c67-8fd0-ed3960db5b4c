<template>
  <div class="player position-relative" style="height: 100%;">
    <div :id="playRegionDomId" style="height: 100%;"></div>
    <FullScreenControl class="position-absolute right-0 bottom-0" v-if="isMobile && !isLandscape" />
  </div>
</template>

<script lang='ts' setup>
import { ref, watch, nextTick, computed, onUnmounted } from 'vue';
import { trtcInstance } from '@/services/trtc';
import useBasicStore from '@/store/basic';
import {StreamInfo} from '@/store/room';
import FullScreenControl from '@/components/FullScreenControl.vue';
import useOrientationWatch from '@/hooks/useOrientationWatch';
import { isMobile }  from '@/utils/utils';

interface Props {
  stream: StreamInfo,
}

const props = defineProps<Props>();
const basicStore = useBasicStore();
const playRegionDomId = computed(() => `${props.stream.userId}_${props.stream.streamType}`);
const loading = ref(false);
const {isLandscape} = useOrientationWatch()

const startPlayRemoteVideo = async () => {
  const { userId, streamType } = props.stream;
  // Play remote stream
  loading.value = true;
  await trtcInstance.startRemoteVideo({
    userId,
    streamType,
    view: `${playRegionDomId.value}`,
    option: {
      fillMode: 'contain'
    }
  });
  // Playing remote stream successfully
  loading.value = false;
};
const stopPlayRemoteVideo = async () => {
  const { userId, streamType } = props.stream;
  loading.value = false;
  await trtcInstance.stopRemoteVideo({ userId, streamType });
};

onUnmounted(() => {
  stopPlayRemoteVideo()
})

watch(
  () => [props.stream.hasVideoStream, props.stream.isVisible],
  async (val) => {
    const [hasVideoStream, isVisible] = val;
    if (hasVideoStream && isVisible) {
      await nextTick();
      const userIdEl = document.getElementById(`${playRegionDomId.value}`) as HTMLDivElement;
      if (userIdEl) {
        if (props.stream.userId === basicStore.userId) {
          await trtcInstance.updateLocalVideo({
            view: `${playRegionDomId.value}`,
          });
        } else {
          await startPlayRemoteVideo();
        };
      }
    }
  },
  { immediate: true },
);


</script>

<style lang="scss">
.player {
  video {
    display: block;
  }
}
</style>