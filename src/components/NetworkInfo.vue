<template>
  <v-chip v-if="networkQuality === 1 || networkQuality === 2" prepend-icon="mdi-signal-cellular-3" variant="text" color="success">
    稳定
  </v-chip>
  <v-chip v-else-if="networkQuality === 3 || networkQuality === 4" prepend-icon="mdi-signal-cellular-2" variant="text" color="warning">
    波动
  </v-chip>
  <v-chip v-else-if="networkQuality === 5" prepend-icon="mdi-signal-cellular-1" variant="text" color="error">
    卡顿
  </v-chip>
  <v-chip v-else-if="networkQuality === 6" prepend-icon="mdi-wifi-remove" variant="text" color="grey-darken-1">
    已断开
  </v-chip>
</template>

<script lang='ts' setup>
import { ref, onMounted, onUnmounted } from 'vue';
import TRTC, { trtcInstance } from '@/services/trtc';

let networkQuality = ref(0)

onMounted(() => {
  trtcInstance.on(TRTC.EVENT.NETWORK_QUALITY, onNetworkQuality)
})
onUnmounted(() => {
  trtcInstance.off(TRTC.EVENT.NETWORK_QUALITY, onNetworkQuality)
})

function onNetworkQuality(event: {downlinkNetworkQuality: number, uplinkNetworkQuality: number}) {
  networkQuality.value = Math.max(event.downlinkNetworkQuality, event.uplinkNetworkQuality)
}
</script>