<template>
<v-btn
  density="comfortable"
  icon="mdi-fullscreen"
  variant="text"
  class="bg-mc-bule-darken-3 opacity-60"
  @click="fullscreen">
  <v-icon class="w-auto h-auto pa-1 rounded-pill"></v-icon>
</v-btn>
</template>

<script lang='ts' setup>
import useOrientationWatch from '@/hooks/useOrientationWatch';
import {MCProtocol} from '@simplex/simple-base'

const {getLandscape} = useOrientationWatch()
function fullscreen() {
  const orientation = getLandscape() ? 'portrait' : 'landscape'
  MCProtocol.Core.Web.setting({
    orientation,
    noTopInset: true,
    title: '直播',
    menu: false,
    titleBar: false,
  });
}
</script>
