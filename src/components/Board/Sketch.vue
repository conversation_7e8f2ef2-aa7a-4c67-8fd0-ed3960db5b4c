<template>
  <div id="sketch"></div>
</template>

<script lang='ts' setup>
import { watch, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
import { Handler } from 'mitt';
import TUIMessage from '@/components/TUI/Message/index';
import {TEduBoard, boardInstance, createBoard, BoardInitParams } from '@/services/board';
import useBoardStore from '@/store/board';
import useBasicStore from '@/store/basic';
import useRoomStore from '@/store/room';
import eventBus from '@/hooks/useMitt';
import {trackEvent, getUrl, getRandomR} from '@/utils/utils'

const basicStore = useBasicStore();
const boardStore = useBoardStore()
const roomStore = useRoomStore()
const { appContext } = getCurrentInstance()!

// let checkTimer: NodeJS.Timeout

watch(
  () => boardStore.isSignalReady,
  async (value) => {
    if (value) {
      // 信令通道准备好了后，则开始初始化白板
      initBoard();
      initEvent();
    }
})

onMounted(() => {
  eventBus.on('tiw-recv-sync-data', addSyncData as Handler);
  eventBus.on('tiw-add-element', addElement as Handler);
})
onBeforeUnmount(() => {
  destroyBoard()
})

function initBoard() {
  destroyBoard();
  let {whUserId, whUserSig} = boardStore
  if (!roomStore.isMaster) {
    whUserId = basicStore.userId
    whUserSig = basicStore.userSig
  }
  const initParams: BoardInitParams = {
    id: 'sketch',
    sdkAppId: basicStore.sdkAppId,
    userId: whUserId,
    userSig: whUserSig,
    classId: basicStore.roomId,
    config: {
      boardContentFitMode:
        TEduBoard.TEduBoardContentFitMode
          .TEDU_BOARD_FILE_FIT_MODE_CENTER_INSIDE,
      h5PPTDownGradeTimeoutTimes: 0,
    },

    styleConfig: {
      brushThin: 50,
      selectBoxColor: '#888',
      selectAnchorColor: '#888',
      scrollbarThumbColor: sessionStorage.getItem(
        'boardScrollbarThumbColor'
      ) || '',
      scrollbarTrackColor: sessionStorage.getItem(
        'boardScrollbarTrackColor'
      ) || '',
    },
    authConfig: {
      dataSyncEnable: roomStore.isMaster
    }
  }
  console.log('=== Board init parameters:', initParams);
  createBoard(initParams);
  // 设置橡皮擦自定义图标
  // boardInstance.setCursorIcon(
  //   TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ERASER,
  //   {
  //     cursor: 'url',
  //     url: 'http://test-1259648581.file.myqcloud.com/image/eraser_32.svg',
  //     offsetX: 16,
  //     offsetY: 16,
  //   }
  // );

  // 设置画笔自定义图标
  // boardInstance.setCursorIcon(
  //   TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN,
  //   {
  //     cursor: 'url',
  //     url: 'https://demo.qcloudtiw.com/web/latest/lead-pencil.svg',
  //     offsetX: 2,
  //     offsetY: 10,
  //   }
  // );

  // if (roomStore.isMaster) {
  //   startCheck()
  // }

  trackEvent({
    fragmentName1: '白板工具',
    actionType: '触发',
    actionName: 'sdk初始化',
  })
}

function initEvent() {
  // 监听错误事件
  boardInstance.on(
    TEduBoard.EVENT.TEB_ERROR,
    (errorCode: any, errorMessage: any) => {
      console.log(
        '======================:  ',
        'TEB_ERROR',
        ' errorCode:',
        errorCode,
        ' errorMessage:',
        errorMessage
      );
      let message = '';
      switch (errorCode) {
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_INIT:
          message = '初始化失败，请重试';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_AUTH:
          message = '服务鉴权失败，请先购买服务';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_LOAD:
          message = '白板加载失败，请重试';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_HISTORYDATA:
          message = '同步历史数据失败，请重试';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_RUNTIME:
          message = '白板运行错误，请检查sdkAppId，userId, userSig是否正确';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_AUTH_TIMEOUT:
          message = '服务鉴权超时，请重试';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_MAX_BOARD_LIMITED:
          message = '单课堂内白板页数已经到达上限';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_SIGNATURE_EXPIRED:
          message =
            'userSig过期了，请重新生成新的userSig，再重新初始化白板';
          break;
      }
      TUIMessage({
        type: 'error',
        message: message,
        duration: 3000,
        appContext
      })
      boardStore.setSignalReady(false)
    }
  );

  // 监听警告事件
  boardInstance.on(
    TEduBoard.EVENT.TEB_WARNING,
    (warnCode: any, warnMessage: any) => {
      console.warn(
        '======================:  ',
        'TEB_WARNING',
        ' warnCode:',
        warnCode,
        ' warnMessage:',
        warnMessage
      );

      let message = '';
      switch (warnCode) {
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_SYNC_DATA_PARSE_FAILED:
          message = '实时数据格式错误，请检查白板信令是否有进行二次包装';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_H5PPT_ALREADY_EXISTS:
          message = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WANNING_ILLEGAL_OPERATION:
          message = '非法操作，请在历史数据完成回调后再调用sdk相关接口';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_H5FILE_ALREADY_EXISTS:
          message = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_VIDEO_ALREADY_EXISTS:
          message = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_IMAGESFILE_ALREADY_EXISTS:
          message = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_GRAFFITI_LOST:
          message = '涂鸦丢失';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_CUSTOM_GRAPH_URL_NON_EXISTS:
          message = '自定义图形url为空';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_IMAGESFILE_TOO_LARGE:
          message = '图片组超大';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_IMAGE_COURSEWARE_ALREADY_EXISTS:
          message = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_IMAGE_MEDIA_BITRATE_TOO_LARGE:
          message =
            '多媒体资源码率大于2048kb/s，网络不好情况下容易造成卡顿，建议对视频码率进行压缩';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_IMAGE_WATERMARK_ALREADY_EXISTS:
          message = '已经存在图片水印，不能重复添加';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_FORMULA_LIB_NOT_LOADED:
          message = '数学公式库没有重新加载';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_ILLEGAL_FORMULA_EXPRESSION:
          message = '非法的数学公式';
          break;
        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_TEXT_WATERMARK_ALREADY_EXISTS:
          message = '已经存在文本水印，不能重复添加';
          break;

        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_EXPORTIMPORT_FILTERRULE_ILLEGAL:
          message = '已经存在文本水印，不能重复添加';
          break;

        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_ELEMENTTYPE_NOT_EXISTS:
          message = '元素类型不存在';
          break;

        case TEduBoard.TEduBoardWarningCode
          .TEDU_BOARD_WARNING_ELEMENTID_NOT_EXISTS:
          message = '元素ID不存在';
          break;
      }
      showErrorTip(message);
      console.warn(message);
    }
  );

  // 白板历史数据同步完成回调
  boardInstance.on(TEduBoard.EVENT.TEB_HISTROYDATA_SYNCCOMPLETED, () => {
    console.log(
      '======================:  ',
      'TEB_HISTROYDATA_SYNCCOMPLETED'
    );
    boardStore.setCurrentFile(
      boardInstance.getFileInfo(boardInstance.getCurrentFile())
    );
    // 设置开启笔锋
    boardInstance.setHandwritingEnable(true);
    // 设置开启点击擦除
    boardInstance.setPiecewiseErasureEnable(true);
    // 白板已经ready了
    boardStore.setTiwReady(true)
  });

  boardInstance.on(TEduBoard.EVENT.TEB_SYNCDATA, (data: any) => {
    eventBus.emit('tiw-send-sync-data', data);
  });

  // 切换文件回调
  boardInstance.on(TEduBoard.EVENT.TEB_SWITCHFILE, (fid: any) => {
    boardStore.setCurrentFile(boardInstance.getFileInfo(fid));
  });

  // 跳转白板页回调
  boardInstance.on(TEduBoard.EVENT.TEB_GOTOBOARD, (_boardId: any, fid: any) => {
    boardStore.setCurrentFile(boardInstance.getFileInfo(fid));
  });

  // 缩放白板页回调
  boardInstance.on(
    TEduBoard.EVENT.TEB_ZOOM_DRAG_STATUS,
    ({ boardId, scale }: { boardId: any, scale: any }) => {
      console.log({ boardId, scale });
      boardStore.setCurrentFile(boardInstance.getFileInfo());
    }
  );

  // 监听截图事件，image为截图内容的base64数据
  boardInstance.on(TEduBoard.EVENT.TEB_SNAPSHOT, ({ image }: any) => {
    const downloadEl = document.createElement('a');
    const event = new MouseEvent('click');
    downloadEl.download = Date.now() + '.png';
    downloadEl.href = image;
    downloadEl.dispatchEvent(event);
  });

  // 白板缩放回调事件
  boardInstance.on(TEduBoard.EVENT.TEB_BOARD_SCALE_CHANGE, (data: any) => {
    console.log(
      '======================:  TEB_BOARD_SCALE_CHANGE',
      ' boardId:',
      data.boardId,
      ' scale:',
      data.scale,
      'xOffset:',
      data.xOffset,
      'yOffset:',
      data.yOffset
    );
  });

  // 绘制动作切换事件
  boardInstance.on(TEduBoard.EVENT.TEB_DRAW_STATUS_CHANGED, (code: any) => {
    console.log('======================: TEB_DRAW_STATUS_CHANGED', code);
  });
  // 添加课件事件
  boardInstance.on(TEduBoard.EVENT.TEB_ADDTRANSCODEFILE, (fid: any) => {
    console.log(
      '======================: TEB_ADDTRANSCODEFILE回调, 文件id:',
      fid
    );
  });

  boardInstance.on(TEduBoard.EVENT.TIW_RESET_DATA, () => {
    console.log('========= TIW_RESET_DATA', '重置白板');
  });

  boardInstance.on(TEduBoard.EVENT.TEB_TEXT_ELEMENT_WARNING, () => {
    console.log('========= TEB_TEXT_ELEMENT_WARNING', '文本高度达到上限');
  });
}

function destroyBoard() {
  if (boardInstance) {
    // 如果白板存在，则先销毁掉，避免页面多个白板对象
    boardInstance.destroy();
    // todo teduBoard = null;
    boardStore.setTiwReady(false)
  }
  // clearInterval(checkTimer)
}

function addSyncData(realtimeData: {}) {
  if (boardInstance) {
    boardInstance.addSyncData(realtimeData);
  }
}

function addElement(url:string) {
  const el = boardInstance.getBoardElementList() as {type: number, elementId: string}[]
  const elements = el.filter(item => item.type === 2)
  if (elements?.length) {
    boardInstance.removeElement(elements[0].elementId)
  }
  boardInstance.addElement(
    TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_H5,
    getUrl(url, {
      _r: getRandomR(1)
    }),
    { erasable: false }
  );
}

function showErrorTip(tip: string) {
  TUIMessage({
    type: 'error',
    message: tip,
    duration: 3000,
    appContext
  })
}

// function startCheck() {
//   checkTimer = setInterval(() => {
//     eventBus.emit('tiw-send-sync-data', {seq: Math.random(), timestamp: +new Date(), value: {}});
//     console.log('startCheck', new Date())
//   }, 60000)
// }

</script>

<style lang="scss" scoped>
#sketch {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
</style>