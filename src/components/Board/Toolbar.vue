<template>
  <v-list
    density="compact"
    class="px-0 py-2 bg-mc-bule-darken-2"
    :class="toolbarClassName"
  >
    <v-list-item class="pa-0">
      <v-tooltip location="end">
        <template v-slot:activator="{ props }">
          <v-btn
            variant="text"
            v-bind="props"
            icon
            tile
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE
              )
            "
          >
          <v-icon>mdi-cursor-default-outline</v-icon>
          </v-btn>
        </template>
        <span>鼠标</span>
      </v-tooltip>
    </v-list-item>

    <v-list-item class="pa-0" style="position: relative">
      <v-menu transition="slide-x-transition" location="end" :offset="[0, -20]">
        <template v-slot:activator="{ props: menuProps }">
          <v-tooltip location="end">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="{ ...menuProps, ...props }"
                @click="
                  setPenToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN
                  )
                "
              >
                <v-icon>mdi-draw</v-icon>
              </v-btn>
            </template>
            <span>涂鸦工具</span>
          </v-tooltip>
        </template>

        <v-card>
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="props"
                @click="
                  setPenToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN
                  )
                "
              >
                <v-icon>mdi-draw</v-icon>
              </v-btn>
            </template>
            <span>画笔</span>
          </v-tooltip>

          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="props"
                @click="
                  setToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_HIGHLIGHTER
                  )
                "
              >
                <v-icon>mdi-pencil-outline</v-icon>
              </v-btn>
            </template>
            <span>荧光笔</span>
          </v-tooltip>
        </v-card>
      </v-menu>
    </v-list-item>
    
    <v-list-item class="pa-0" style="position: relative">
      <v-menu transition="slide-x-transition" location="end" :offset="[0, -20]">
        <template v-slot:activator="{ props: menuProps }">
          <v-tooltip location="end">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="{ ...menuProps, ...props }"
                @click="
                  setToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT
                  )
                "
              >
                <v-icon>mdi-rectangle-outline</v-icon>
              </v-btn>
            </template>
            <span>几何图形</span>
          </v-tooltip>
        </template>

        <v-card>
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="props"
                @click="
                  setToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LINE,
                    true,
                    false,
                    1,
                    1
                  )
                "
              >
                <v-icon>mdi-minus</v-icon>
              </v-btn>
            </template>
            <span>直线</span>
          </v-tooltip>

          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="props"
                @click="
                  setToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT
                  )
                "
              >
                <v-icon>mdi-rectangle-outline</v-icon>
              </v-btn>
            </template>
            <span>矩形</span>
          </v-tooltip>

          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="props"
                @click="
                  setToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_OVAL
                  )
                "
              >
                <v-icon>mdi-ellipse-outline</v-icon>
              </v-btn>
            </template>
            <span>椭圆</span>
          </v-tooltip>

          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="props"
                @click="
                  setToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_CIRCLE
                  )
                "
              >
                <v-icon>mdi-circle-outline</v-icon>
              </v-btn>
            </template>
            <span>圆</span>
          </v-tooltip>
        </v-card>
      </v-menu>
    </v-list-item>

    <v-list-item class="pa-0" style="position: relative">
      <v-menu transition="slide-x-transition" location="end" :offset="[0, -20]">
        <template v-slot:activator="{ props: menuProps }">
          <v-tooltip location="end">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                v-bind="{ ...menuProps, ...props }"
                icon
                tile
                @click="
                  setToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ERASER
                  )
                "
              >
                <v-icon>mdi-eraser</v-icon>
              </v-btn>
            </template>
            <span>橡皮擦</span>
          </v-tooltip>
        </template>
        <v-card>
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="props"
                @click="setPiecewiseErasureEnable(false)"
              >
                <v-icon>mdi-eraser</v-icon>
              </v-btn>
            </template>
            <span>整段擦除</span>
          </v-tooltip>

          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="props"
                @click="setPiecewiseErasureEnable(true)"
              >
                <v-icon>mdi-eraser-variant</v-icon>
              </v-btn>
            </template>
            <span>分段擦除</span>
          </v-tooltip>
        </v-card>
      </v-menu>
    </v-list-item>

    <v-list-item class="pa-0">
      <v-tooltip location="end">
        <template v-slot:activator="{ props }">
          <v-btn
            variant="text"
            v-bind="props"
            icon
            tile
            @click="
              setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_TEXT)
            "
          >
            <v-icon>mdi-format-text</v-icon>
          </v-btn>
        </template>
        <span>文本</span>
      </v-tooltip>
    </v-list-item>

    <v-list-item class="pa-0">
      <v-tooltip location="end">
        <template v-slot:activator="{ props }">
          <v-btn
            variant="text"
            v-bind="props"
            icon
            tile
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LASER
              )
            "
          >
            <v-icon>mdi-flare</v-icon>
          </v-btn>
        </template>
        <span>激光笔</span>
      </v-tooltip>
    </v-list-item>

    <v-list-item class="pa-0">
      <v-tooltip location="end">
        <template v-slot:activator="{ props }">
          <v-btn
            variant="text"
            v-bind="props"
            icon
            tile
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ZOOM_DRAG
              )
            "
          >
            <v-icon>mdi-cursor-move</v-icon>
          </v-btn>
        </template>
        <span>缩放移动</span>
      </v-tooltip>
    </v-list-item>

    <v-list-item class="pa-0" style="position: relative">
      <v-menu
        transition="slide-x-transition"
        location="end"
        :offset="[0, -20]"
        :close-on-content-click="false"
      >
        <template v-slot:activator="{ props: menuProps }">
          <v-tooltip location="end">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                tile
                v-bind="{ ...menuProps, ...props }"
              >
                <v-icon>mdi-palette</v-icon>
              </v-btn>
            </template>
            <span>工具样式</span>
          </v-tooltip>
        </template>
        <v-card>
          <v-card-text>
            <div>
              <span>画笔粗细</span>
              <v-slider
                v-model="thin"
                hide-details
                :thumb-size="24"
                :label="String(thin)"
                step="10"
                max="500"
                min="10"
                @update:modelValue="setBrushThin"
              ></v-slider>
            </div>
            <div>
              <span>橡皮擦大小（像素）</span>
              <v-slider
                v-model="eraserSize"
                hide-details
                :thumb-size="24"
                :label="String(eraserSize)"
                step="1"
                max="100"
                min="16"
                @update:modelValue="setEraserSize"
              ></v-slider>
            </div>
            <div>
              <span>文本大小</span>
              <v-select
                :items="textSizeItems"
                style="width: 100px"
                label="文本大小"
                hide-details
                @update:modelValue="handleTextSizeChange"
                single-line
                v-model="fontSize"
                :item-title="'text'"
              ></v-select>
            </div>
            <div>
              <span>行高设置</span>
              <v-select
                :items="lineHeightItems"
                style="width: 100px"
                label="行高设置"
                hide-details
                @update:modelValue="handleLineHeightChange"
                single-line
                v-model="lineHeight"
                :item-title="'text'"
              ></v-select>
            </div>
            <span>画笔颜色</span>
            <v-color-picker
              v-model="brushColor"
              width="300"
              canvas-height="100"
              flat
              hide-sliders
              hide-inputs
              class="ma-2"
              show-swatches
              :swatches="[
                ['#FF0000'],
                ['#FFFF00'],
                ['#00FF00'],
                ['#00FFFF'],
                ['#0000FF'],
              ]"
              @update:modelValue="setBrushColor"
            ></v-color-picker>
            <span>文本工具颜色</span>
            <v-color-picker
              v-model="textColor"
              width="300"
              canvas-height="100"
              flat
              hide-sliders
              hide-inputs
              class="ma-2"
              show-swatches
              :swatches="[
                ['#FF0000'],
                ['#FFFF00'],
                ['#00FF00'],
                ['#00FFFF'],
                ['#0000FF'],
              ]"
              @update:modelValue="setTextColor"
            ></v-color-picker>
            <span>荧光笔颜色</span>
            <!-- 此处初始值是为了防止首次打开面板时背景色被更改 -->
            <v-color-picker
              v-model="highlighterColor"
              mode="rgba"
              width="300"
              hide-canvas
              hide-inputs
              flat
              @update:modelValue="setHighlighterColor"
            ></v-color-picker>
            <span>背景颜色</span>
            <!-- 此处初始值是为了防止首次打开面板时背景色被更改 -->
            <v-color-picker
              v-model="backgroundColor"
              mode="rgba"
              width="300"
              hide-canvas
              hide-inputs
              flat
              @update:modelValue="setBackgroundColor"
            ></v-color-picker>
          </v-card-text>
        </v-card>
      </v-menu>
    </v-list-item>

    <v-list-item class="pa-0">
      <v-menu transition="slide-x-transition" location="end" :offset="[0, -20]">
        <template v-slot:activator="{ props: menuProps }">
          <v-tooltip location="end">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                v-bind="{ ...menuProps, ...props }"
                icon
                tile
              >
                <v-icon>mdi-broom</v-icon>
              </v-btn>
            </template>
            <span>一键清空</span>
          </v-tooltip>
        </template>
        <v-list>
          <v-list-item @click="cleanAll()">清空涂鸦</v-list-item>
          <v-list-item @click="cleanAll(true)">清空白板</v-list-item>
        </v-list>
      </v-menu>
    </v-list-item>

    <v-list-item class="pa-0">
      <v-dialog :max-width="320" persistent>
        <template v-slot:activator="{ props: menuProps }">
          <v-tooltip location="end">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                v-bind="{ ...menuProps, ...props }"
                icon
                tile
              >
                <v-icon>mdi-cog-outline</v-icon>
              </v-btn>
            </template>
            <span>快捷键设置</span>
          </v-tooltip>
        </template>
        <template v-slot:default="{ isActive }">
          <Shortcut @closeSelf="isActive.value = false" @onSave="getShortcutSetting" />
        </template>
      </v-dialog>
    </v-list-item>

    <!-- <v-list-item class="pa-0">
      <v-tooltip location="end">
        <template v-slot:activator="{ props }">
          <v-btn
            variant="text"
            v-bind="props"
            icon
            tile
            @click="
              addSyncTest()
            "
          >
            <v-icon>mdi-gift</v-icon>
          </v-btn>
        </template>
        <span>同步测试</span>
      </v-tooltip>
    </v-list-item> -->
  </v-list>
</template>

<script lang='ts' setup>
import { watch, ref, onMounted, onUnmounted } from 'vue';
import { Handler } from 'mitt';
import {TEduBoard, boardInstance } from '@/services/board';
import useBoardStore from '@/store/board';
import Shortcut from '../Shortcut.vue';
import {isMacOs} from '@/utils/utils';
import eventBus from '@/hooks/useMitt';

const localKey = 'shortcut-setting';
let shortcutSetting = {} as {[key: string]: any;};
const boardStore = useBoardStore()
let currentToolType = ref(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN)
let toolbarClassName = ref('')
let brushColor = ref('')
let textColor = ref('')
let highlighterColor = ref({ r: 221, g: 253, b: 142, a: 0.5 })
let backgroundColor = ref({ r: 255, g: 0, b: 0, a: 1 })
let thin = ref(50)
let eraserSize = ref(16)
let fontSize = ref(1)
let textSizeItems = ref([
  // 1、1.15、1.3、1.5、2、3
  {
    text: '14',
    value: 1,
  },
  {
    text: '16',
    value: 1.15,
  },
  {
    text: '18',
    value: 1.3,
  },
  {
    text: '21',
    value: 1.5,
  },
  {
    text: '28',
    value: 2,
  },
  {
    text: '42',
    value: 3,
  },
])
let lineHeight = ref(1)
let lineHeightItems = ref([
  // 1、1.15、1.3、1.5、2、3
  {
    text: '1',
    value: 1,
  },
  {
    text: '1.15',
    value: 1.15,
  },
  {
    text: '1.3',
    value: 1.3,
  },
  {
    text: '1.5',
    value: 1.5,
  },
  {
    text: '2',
    value: 2,
  },
  {
    text: '3',
    value: 3,
  },
])
function capitalizeFirstLetter(str: string): string {
  if (!str) return '';
  return str.charAt(0).toLocaleUpperCase() + str.slice(1);
}
function getKeyComp(event: KeyboardEvent): string {
  let keyList = []
  if (event.ctrlKey) {
    keyList.push('Ctrl')
  }
  if (event.metaKey && isMacOs) {
    keyList.push('Command')
  }
  if (event.shiftKey) {
    keyList.push('Shift')
  }
  if (event.altKey) {
    keyList.push('Alt')
  }
  if (event.key && !['Control', 'Meta', 'Shift', 'Alt'].includes(event.key)) {
    keyList.push(capitalizeFirstLetter(event.key))
  }
  return keyList.join(' + ')
}
function getShortcutSetting() {
  const setting = JSON.parse(localStorage.getItem(localKey) || '{}');
  shortcutSetting = setting
}
onMounted(() => {
  getShortcutSetting()
  window.addEventListener('keydown', handleKeydown);
  eventBus.on('tiw-keyboard', handleKeydown as Handler);
})
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
})

function handleKeydown(event: KeyboardEvent) {
  if (!boardStore.isTiwReady) {
    return
  }
  const keys = getKeyComp(event);
  Object.keys(shortcutSetting).forEach((key) => {
    if (keys === shortcutSetting[key]) {
      switch (key) {
        case 'mouse':
          setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE);
          break;
        case 'brush':
          setPenToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN);
          break;
        case 'shape':
          setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT);
          break;
        case 'text':
          setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_TEXT);
          break;
        case 'clearBoard':
          cleanAll();
          break;
      }
      if (event.preventDefault) {
        event.preventDefault()
      }
    }
  });
}

watch(
  () => boardStore.isTiwReady,
  (value) => {
    if (value) {
      // 监听元素位置更新事件
      boardInstance.on(
        TEduBoard.EVENT.TEB_BOARD_ELEMENT_POSITION_CHANGE,
        (_data: any, status: any) => {
          if (
            status ===
            TEduBoard.TEduBoardPositionChangeStatus
              .TEDU_BOARD_POSITION_CHANGE_START
          ) {
            // 开始变化
            toolbarClassName.value = 'disabled-event';
          } else if (
            status ===
            TEduBoard.TEduBoardPositionChangeStatus
              .TEDU_BOARD_POSITION_CHANGE_END
          ) {
            // 结束变化
            toolbarClassName.value = '';
          }
        }
      );
    }
  },
  {immediate: true}
)

function setToolType(
  toolType: any,
  isSolidLine = true,
  isFill = false,
  startArrowType = 1,
  endArrowType = 1
) {
  console.log(
    'setToolType:',
    isSolidLine,
    isFill,
    startArrowType,
    endArrowType
  );
  currentToolType.value = toolType;
  boardInstance.setToolType(toolType);
  let lineType = null;
  if (isSolidLine) {
    lineType = TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_SOLID;
  } else {
    lineType = TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_DOTTED;
  }
  boardInstance.setGraphStyle({
    lineType: lineType,
    fillType: isFill
      ? TEduBoard.TEduBoardFillType.SOLID
      : TEduBoard.TEduBoardFillType.NONE,
    startArrowType: startArrowType,
    endArrowType: endArrowType,
  });
}

/**
 * 工具类型
 * @param {*} toolType 画笔工具
 */
 function setPenToolType(toolType: any) {
  currentToolType.value = toolType;
  boardInstance.setToolType(toolType);

  boardInstance.enablePenAutoFit(false);
  boardInstance.setPenAutoFittingMode(
    TEduBoard.TEduBoardPenFittingMode.NONE
  );
}

function setBrushThin(thin: number) {
  boardInstance.setBrushThin(thin);
}

function setEraserSize(eraserSize: number) {
  boardInstance.setEraserSize(eraserSize);
}

function setBrushColor(color: string) {
  boardInstance.setBrushColor(color);
}

function setTextColor(color: string) {
  boardInstance.setTextColor(color);
}

function setBackgroundColor(color: {r: number, g: number, b: number, a: number}) {
  const r = color.r;
  const g = color.g;
  const b = color.b;
  let a = Number(color.a.toFixed(1)).valueOf();
  a = Math.min(0.9, Math.max(a, 0.1));
  const rgbaColor = `rgba(${r}, ${g}, ${b}, ${a})`;
  boardInstance.setBackgroundColor(rgbaColor);
}

function setHighlighterColor(color: {r: number, g: number, b: number, a: number}) {
  const r = color.r;
  const g = color.g;
  const b = color.b;
  let a = Number(color.a.toFixed(1)).valueOf();
  a = Math.min(0.9, Math.max(a, 0.1));
  const highlighterColor = `rgba(${r}, ${g}, ${b}, ${a})`;
  boardInstance.setHighlighterColor(highlighterColor);
}

function cleanAll(removeBackground = false) {
  let elements: string[] = []
  if (!removeBackground) {
    const el = boardInstance.getBoardElementList() as {type: number, elementId: string}[]
    elements = el.filter(item => item.type === 2).map(item => item.elementId)
    if (elements.length) {
      boardInstance.lockElements(elements, true)
    }
  }
  boardInstance.clear(removeBackground);
  if (elements.length) {
    boardInstance.lockElements(elements, false)
  }
}

function setPiecewiseErasureEnable(enable: boolean) {
  boardInstance.setPiecewiseErasureEnable(enable);
}

function handleTextSizeChange(value: number) {
  boardInstance.setTextSize(value * 320);
}

function handleLineHeightChange(value: number) {
  boardInstance.setTextLineHeight(value);
}
// function addSyncTest() {
//   boardInstance.addElement(
//     TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_H5,
//     'https://tic-res-1259648581.cos.ap-shanghai.myqcloud.com/board/h5webctrl/h5web.html',
//     { erasable: false }
//   );
// }

</script>

<style scoped>
.toolbar-container.disabled-event {
  pointer-events: none;
}
</style>