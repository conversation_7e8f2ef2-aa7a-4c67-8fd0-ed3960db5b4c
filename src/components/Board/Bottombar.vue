<template>
  <v-toolbar floating density="compact" class="bg-mc-bule-darken-8">
    <template v-if="show">
      <v-tooltip location="top">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" tile icon @click="undo()">
            <v-icon>mdi-undo-variant</v-icon>
          </v-btn>
        </template>
        <span>撤销</span>
      </v-tooltip>
  
      <v-tooltip location="top">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" tile icon @click="redo()">
            <v-icon>mdi-redo-variant</v-icon>
          </v-btn>
        </template>
        <span>重做</span>
      </v-tooltip>

      <v-divider inset vertical></v-divider>

      <v-tooltip location="top">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" tile icon @click="setBoardScale(100)">
            <v-icon>mdi-target</v-icon>
          </v-btn>
        </template>
        <span>重置比例</span>
      </v-tooltip>

      <v-tooltip location="top">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" tile icon :disabled="boardScale <= 100" @click="setBoardScale(boardScale - 10)">
            <v-icon>mdi-minus</v-icon>
          </v-btn>
        </template>
        <span>缩小</span>
      </v-tooltip>

      <span>{{ boardScale }}%</span>

      <v-tooltip location="top">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" tile icon @click="setBoardScale(boardScale + 10)">
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </template>
        <span>放大</span>
      </v-tooltip>

      <v-divider inset vertical></v-divider>

      <v-tooltip location="top">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" tile icon @click="prevPage()">
            <v-icon>mdi-page-first</v-icon>
          </v-btn>
        </template>
        <span>上一页</span>
      </v-tooltip>

      <span>
        {{ currentFile.currentPageIndex + 1 }} / {{ currentFile.pageCount }}
      </span>

      <v-tooltip location="top">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" tile icon @click="nextPage()">
            <v-icon>mdi-page-last</v-icon>
          </v-btn>
        </template>
        <span>下一页</span>
      </v-tooltip>

      <v-tooltip location="top">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" tile icon @click="addBoard()">
            <v-icon>mdi-text-box-plus-outline</v-icon>
          </v-btn>
        </template>
        <span>添加白板</span>
      </v-tooltip>

      <v-tooltip location="top">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" tile icon @click="deleteBoard()">
            <v-icon>mdi-text-box-minus-outline</v-icon>
          </v-btn>
        </template>
        <span>删除白板</span>
      </v-tooltip>
    </template>
    
    <v-btn icon tile :style="{ width: `${show ? '16px' : '36px'}` }" @click="show = !show">
      <v-icon>
        {{ show ? 'mdi-chevron-left' : 'mdi-chevron-right' }}
      </v-icon>
    </v-btn>
  </v-toolbar>
</template>

<script lang='ts' setup>
import { watch, ref } from 'vue';
import useBoardStore from '@/store/board';
import { TEduBoard, boardInstance } from '@/services/board';

const boardStore = useBoardStore()
let boardScale = ref(100)
let currentFile = ref({
  currentPageIndex: 0,
  pageCount: 0,
})
let show = ref(true)

watch(() => boardStore.current.fileInfo, (value) => {
  currentFile.value = value as {currentPageIndex: number, pageCount: number};
  boardScale.value = boardInstance.getBoardScale();
}, {
  immediate: true
})

// 切换文件回调
boardInstance.on(TEduBoard.EVENT.TEB_BOARD_SCALE_CHANGE, () => {
  boardScale.value = boardInstance.getBoardScale();
});

function setBoardScale(scale: number) {
  boardScale.value = scale;
  boardInstance.setBoardScale(boardScale.value);
  boardScale.value = boardInstance.getBoardScale();
}
function undo() {
  boardInstance.undo();
}
function redo() {
  boardInstance.redo();
}
function prevPage() {
  boardInstance.prevBoard();
}
function nextPage() {
  boardInstance.nextBoard();
}
function addBoard() {
  boardInstance.addBoard();
}
function deleteBoard() {
  boardInstance.deleteBoard();
}

</script>