<template>
    <div class="position-relative">
      <sketch class="mx-auto" :style="{width: `${width}px`, height: `${height}px`}"></sketch>
      <template v-if="boardStore.isTiwReady && roomStore.isMaster">
        <toolbar class="toolbar-style"></toolbar>
        <bottombar class="bottombar-style"></bottombar>
      </template>
    </div>
</template>

<script lang='ts' setup>
import { watch, onMounted, onBeforeUnmount, ref, getCurrentInstance } from 'vue';
import TUIMessage from '@/components/TUI/Message/index';
import TIM, { timInstance } from '@/services/tim';
import {Message} from 'tim-js-sdk'
import Sketch from './Sketch.vue';
import Toolbar from './Toolbar.vue';
import Bottombar from './Bottombar.vue';
import {toAwait, trackEvent, sendWarning, errorToObject} from '@/utils/utils'
import useBoardStore from '@/store/board';
import useChatStore from '@/store/chat';
import useBasicStore from '@/store/basic';
import useRoomStore from '@/store/room';
import eventBus from '@/hooks/useMitt';
import useResizeWatch from '@/hooks/useResizeWatch';

interface SendMessageStatusMap {
  [key: string]: {
    resendCount: number
  };
}

const chatStore = useChatStore();
const basicStore = useBasicStore();
const boardStore = useBoardStore()
const roomStore = useRoomStore()
let sendMessageStatus = ref<SendMessageStatusMap>({})
const { appContext } = getCurrentInstance()!
const { width, height } = useResizeWatch()

watch(
  () => chatStore.isTimReady,
  () => {
    joinClassroom();
    eventBus.on('quit-class-room', quitClassroom);
    eventBus.on('tiw-send-sync-data', sendBoardRealtimeDataMessage);
})

async function joinClassroom() {
  // todo initTim()
  _initTimEvent();
  boardStore.setSignalReady(true);
}

async function quitClassroom() {
  await quitSignal()

  boardStore.setSignalReady(false);
}

async function quitSignal() {
  if (timInstance) {
    console.log('Signal _uninitTimEvent');
    timInstance.off(TIM.EVENT.MESSAGE_RECEIVED, _onMessageReceived);
    timInstance.off(TIM.EVENT.ERROR, _onTIMError);
    timInstance.off(TIM.EVENT.KICKED_OUT, _onTIMKickout);
    timInstance.off(TIM.EVENT.NET_STATE_CHANGE, _onTIMNetChange);

    window.removeEventListener('message', handleMessage)
  }
}

function _onTIMError(event: any) {
  TUIMessage({
    type: 'error',
    message: `TIM SDK出现错误，code:${event.data.code}, message:${event.data.message}`,
    duration: 3000,
    appContext
  })
}

async function _onTIMKickout(event: any) {
  const { type } = event.data;
  let tipText = '';
  console.warn('_onTIMKickout:', event);
  switch (type) {
    case TIM.TYPES.KICKED_OUT_MULT_ACCOUNT:
      tipText = '多实例登录被挤下线';
      break;
    case TIM.TYPES.KICKED_OUT_MULT_DEVICE:
      tipText = '多终端登录被挤下线';
      break;
    case TIM.TYPES.KICKED_OUT_USERSIG_EXPIRED:
      tipText = '签名过期被挤下线';
      break;
    default:
      tipText = '被踢下线了';
      break;
  }
  TUIMessage({
    type: 'error',
    message: tipText,
    duration: 0,
    appContext
  })
  boardStore.setSignalReady(false)
  await quitSignal()
  // todo tim = null;
}

function _onTIMNetChange() {}

function _initTimEvent() {
  console.log('Signal _initTimEvent');
  timInstance.on(TIM.EVENT.MESSAGE_RECEIVED, _onMessageReceived);

  timInstance.on(TIM.EVENT.KICKED_OUT, _onTIMKickout);

  timInstance.on(TIM.EVENT.NET_STATE_CHANGE, _onTIMNetChange);

  window.addEventListener('message', handleMessage)
}

function _onMessageReceived(event: { data: any }) {
  const messages = event.data;
  const groupId = String(basicStore.roomId);
  messages.forEach((message: any) => {
    // 群组消息
    if (message.conversationType === TIM.TYPES.CONV_GROUP) {
      if (message.to === groupId) {
        // 如果是当前群组
        const elements = message.getElements();
        if (elements.length) {
          elements.forEach(async (element: any) => {
            if (element.type === 'TIMCustomElem') {
              if (element.content.extension === 'TXWhiteBoardExt') {
                if (message.from != basicStore.userId) {
                  eventBus.emit(
                    'tiw-recv-sync-data',
                    element.content.data
                  );
                }
              }
            }
          });
        }
      }
    }
  });
}

async function sendBoardRealtimeDataMessage(data: any) {
  if (!timInstance) return;
  const groupId = String(basicStore.roomId);
  const message = timInstance.createCustomMessage({
    to: groupId,
    conversationType: TIM.TYPES.CONV_GROUP,
    priority: TIM.TYPES.MSG_PRIORITY_HIGH, // 因为im消息有限频，白板消息的优先级调整为最高
    payload: {
      data: JSON.stringify(data),
      description: '',
      extension: 'TXWhiteBoardExt',
    },
  });

  const [error] = await toAwait(timInstance.sendMessage(message, {
    messageControlInfo: {
      excludedFromContentModeration: true
    }
  }));
  if (error) {
    sendWarning(4, {
      message: {
        to: groupId,
        conversationType: TIM.TYPES.CONV_GROUP,
        priority: TIM.TYPES.MSG_PRIORITY_HIGH,
        payload: {
          data: JSON.stringify(data),
          description: '',
          extension: 'TXWhiteBoardExt',
        },
      },
      content: errorToObject(error as Error)
    })
    sendMessageStatus.value[message.ID] = {
      resendCount: 0,
    }; // 重试次数
    resendBoardRealtimeDataMessage(message);
  }
}

async function resendBoardRealtimeDataMessage(message: Message) {
  console.log(
    '>>>> resendBoardRealtimeDataMessage:',
    sendMessageStatus.value[message.ID].resendCount
  );
  const [error] = await toAwait(timInstance.resendMessage(message));
  if (error && sendMessageStatus.value[message.ID]) {
    sendMessageStatus.value[message.ID].resendCount += 1; // 重试次数+1
    if (sendMessageStatus.value[message.ID].resendCount > 2) {
      // 重试3次后
      TUIMessage({
        type: 'error',
        message: '白板实时信令同步失败',
        duration: 5000,
        appContext
      })
    } else {
      resendBoardRealtimeDataMessage(message)
    }
    sendWarning(4, {
      message,
      content: errorToObject(error as Error)
    })
  } else {
    // 成功后，删除状态
    delete sendMessageStatus.value[message.ID];
  }
}

function handleMessage(e: MessageEvent) {
  let {type, data} = e.data
  if (type === 'openExam' && data.url) {
    eventBus.emit('tiw-add-element', data.url);

    trackEvent({
      fragmentName1: '白板h5网页',
      actionType: '点击',
      actionName: '打开试题列表',
      url: data.url,
    })
  } else if(type === 'keyBoard') {
    eventBus.emit('tiw-keyboard', data);
  }
}

</script>

<style lang="scss" scoped>
.bottombar-style {
  width: auto;
  transform-origin: right;
  position: absolute !important;
  left: 4px;
  bottom: 0;
  z-index: 1;
}

.toolbar-style {
  transform: translateY(-50%);
  position: absolute;
  left: 4px;
  top: 50%;
}
</style>