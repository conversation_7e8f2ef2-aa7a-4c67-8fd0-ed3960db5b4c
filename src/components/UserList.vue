<template>
  <v-col class="overflow-x-auto flex-grow-1 pa-0">
    <template v-for="item in roomStore.userList" :key="item.userId">
      <v-hover>
        <template v-slot:default="{ isHovering, props }">
          <v-row no-gutters
            align="center"
            v-bind="props"
            class="pa-4"
            :class="isHovering ? 'bg-mc-bule-darken-6' : ''"
            >
              <v-col
                cols="auto"
              >
                <v-avatar
                  size="44px"
                  :image="item.avatarUrl"
                >
                </v-avatar>
              </v-col>

              <v-col
                cols="auto"
                class="text-left pl-3"
                style="max-width: 120px;"
              >
                <div class="text-truncate">{{ item.userName || item.userId }}</div>
                <div v-if="item.userRole === USERROLE.TEACHER" class="text-mc-green-lighten-2" style="font-size: 0.75rem;">讲师</div>
                <div v-else-if="item.userRole === USERROLE.STUDENT" class="text-mc-pink-lighten-1" style="font-size: 0.75rem;">学员</div>
                <div v-else-if="item.userRole === USERROLE.SUPERVISOR" class="text-mc-orange-lighten-1" style="font-size: 0.75rem;">督导</div>
              </v-col>

              <v-col
                v-if="roomStore.isMaster"
                class="text-right"
              >
                <v-btn v-if="item.isMessageDisabled" class="bg-mc-red-darken-1 mr-3" variant="text" size="small" @click="setMute(item.userId, 0)" :width="68">
                  禁言中
                </v-btn>
                <v-btn v-else class="bg-mc-bule-darken-4 mr-3" variant="text" size="small" @click="setMute(item.userId, 18000)" :width="68">
                  禁言
                </v-btn>
                <v-btn v-if="!item.hasAudioStream" class="bg-mc-red-darken-1" variant="text" size="small" @click="setDeviceState(item.userId, DEVICETYPE.MICROPHONE, OPERATETYPE.OPENDEVICE)" :width="68">
                  禁音中
                </v-btn>
                <v-btn v-else class="bg-mc-bule-darken-4" variant="text" size="small" @click="setDeviceState(item.userId, DEVICETYPE.MICROPHONE, OPERATETYPE.CLOSEDEVICE)" :width="68">
                  禁音
                </v-btn>
              </v-col>
            </v-row>
        </template>
      </v-hover>
    </template>
  </v-col>
</template>

<script lang='ts' setup>
import TIM, { timInstance } from '@/services/tim';
import useBasicStore from '@/store/basic';
import useRoomStore from '@/store/room';
import {DEVICETYPE, OPERATETYPE, USERROLE} from '@/types/type'

const basicStore = useBasicStore();
const roomStore = useRoomStore();

async function setMute(userId: string, muteTime: number) {
  if (!timInstance) return;
  if (userId === basicStore.userId) return;
  const imResponse = await timInstance.setGroupMemberMuteTime({
    groupID: `${basicStore.roomId}`,
    userID: userId,
    muteTime: muteTime
  });
  const {member: {muteUntil, userID}} = imResponse.data;
  const isMessageDisabled = muteUntil > +new Date() / 1000
  roomStore.setMuteUserChat(userID, isMessageDisabled);
}

async function setDeviceState(userId: string, deviceType: DEVICETYPE, operateType: OPERATETYPE) {
  if (!timInstance) return;
  if (userId === basicStore.userId) return;
  const groupId = String(basicStore.roomId);
  const message = timInstance.createCustomMessage({
    to: groupId,
    conversationType: TIM.TYPES.CONV_GROUP,
    priority: TIM.TYPES.MSG_PRIORITY_NORMAL,
    payload: {
      data: JSON.stringify({
        to: userId,
        groupID: groupId,
        // 音频 1，视频 2
        deviceType: deviceType,
        // 解除 1，禁言 2
        operateType: operateType,
      }),
      description: '',
      extension: 'MCTrainingLiveExt',
    },
  });
  timInstance.sendMessage(message, {
    messageControlInfo: {
      excludedFromContentModeration: true
    }
  })
}

</script>
