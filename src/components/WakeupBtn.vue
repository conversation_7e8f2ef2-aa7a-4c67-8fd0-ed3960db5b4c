<template>
  <div v-if="isMucang" class="launch-app-btn" @click="jump"></div>
  <wx-open-launch-app class="launch-app-btn" appid="wx6fcf65f28d6a3919"
    v-else-if="isAndroid && isWeixin"
    :extinfo="targetUrl" @launch="launch" @error="error">
    <component :is="'script'" type="text/wxtag-template">
      <component :is="'style'">
        .btn { position: absolute; top: 0; right: 0; bottom: 0; left: 0; background: transparent }
      </component>
      <div class="btn"></div>
    </component>
  </wx-open-launch-app>
  <div v-else class="launch-app-btn" @click="goApp"></div>
</template>

<script lang='ts' setup>
import {isAndroid, isMucang, isWeixin, webOpen} from '@/utils/utils'

let timer: NodeJS.Timeout
let donwloadUrl = 'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-jiaoliantuiguang/down1.html?channelCode=zhibo'

interface Props {
  targetUrl: string,
}
const props = defineProps<Props>();

function jump() {
  webOpen({
    url: props.targetUrl,
  })
}
function goApp() {
    location.href = 'https://www.jiakaobaodian.com/mucang-gateway?navUrl=' + encodeURIComponent(props.targetUrl)
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      window.location.href = donwloadUrl
    }, 2000)

    document.addEventListener('visibilitychange', () => {
      clearTimeout(timer)
    })
}
function launch() {
}
function error(e: {detail: string}) {
  console.log('launch fail', e.detail)
  window.location.href = donwloadUrl
}
</script>
<style lang="scss" scoped>
.launch-app-btn {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
</style>
