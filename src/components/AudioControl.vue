<template>
  <v-btn
    v-if="isMobile"
    density="comfortable"
    :icon="!roomStore.localUser.hasAudioStream ? 'mdi-microphone-off' : 'mdi-microphone'"
    variant="text"
    class="bg-mc-bule-darken-3 opacity-60"
    @click="toggleAudio">
    <v-icon class="w-auto h-auto pa-2 rounded-pill" :class="isLandscape ? 'pa-1' : 'pa-2'"></v-icon>
  </v-btn>
  <v-btn
    v-else
    rounded="0"
    stacked
    variant="text"
    class="px-3"
    @click="toggleAudio">
    <template #prepend>
      <v-icon size="48" class="mr-2">
        <img src="../assets/svg/microphone-off.svg" v-if="!roomStore.localUser.hasAudioStream" />
        <img src="../assets/svg/microphone.svg" v-else />
      </v-icon>
    </template>
    麦克风
    <v-menu
      :close-on-content-click="false"
      transition="scale-transition"
      min-width="290px"
    >
      <template v-slot:activator="{ props }">
        <v-btn
          class="position-absolute top-0 right-0"
          style="height: 48px; width: 24px;"
          v-bind="props"
          size="small"
          tile
          icon="mdi-chevron-up"
        >
        </v-btn>
      </template>
      <v-card style="width: 280px;">
        <v-card-title>
          麦克风
        </v-card-title>
        <v-card-text>
          <v-select
            :items="microphoneList"
            hide-details
            single-line
            :item-title="'label'"
            :item-value="'deviceId'"
            variant="outlined"
            :placeholder="microphoneList.length ? '选择麦克风设备' : '没有找到可用的麦克风设备'"
            v-model="currentMicrophoneId"
            :disabled="!microphoneList.length"
          ></v-select>
        </v-card-text>
      </v-card>
    </v-menu>
  </v-btn>
</template>

<script lang='ts' setup>
import { trtcInstance } from '@/services/trtc';
import { storeToRefs } from 'pinia';
import useRoomStore from '@/store/room';
import { isMobile }  from '@/utils/utils';
import useOrientationWatch from '@/hooks/useOrientationWatch';
import useDeviceManager from '@/hooks/useDeviceManager';

const roomStore = useRoomStore();
const { currentMicrophoneId, microphoneList } = storeToRefs(roomStore);
const {initMediaDeviceList, getCurrentDevice} = useDeviceManager()

const {isLandscape} = useOrientationWatch()
const toggleAudio = async () => {
  const hasAudioStream = roomStore.localUser.hasAudioStream
  if (!getCurrentDevice('micphone')) {
    await initMediaDeviceList()
  }
  await trtcInstance.updateLocalAudio({ mute: hasAudioStream });
  roomStore.setLocalUser({hasAudioStream: !hasAudioStream});
};
</script>
