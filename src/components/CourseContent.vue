<template>
<div v-if="roomStore.status === 3" class="d-flex flex-column justify-center align-center h-100">
  <div>课程已结束</div>
  <v-btn class="bg-mc-bule-lighten-1 rounded-pill mt-4" v-if="hasHomeWord" variant="text" @click="toHomeWork">
    去完成课后作业
  </v-btn>
</div>
<Player v-else-if="roomStore.status === 2 && roomStore.whStream" :stream="roomStore.whStream" />
<div v-else class="d-flex justify-center align-center h-100 bg2">
  <div style="margin-top: 116px;">课程内容准备中，请稍后…</div>
</div>
</template>

<script lang='ts' setup>
import { ref, watch } from 'vue';
import useRoomStore from '@/store/room';
import Player from '@/components/Player.vue';
import { URLParams, httpRequest, webOpen } from '@/utils/utils';

async function getHomeWork() {
  return await httpRequest({
    url: 'api/web/course/get-homework-list.htm',
    params: {
      courseScheduleId: URLParams.courseId,
    },
    host: 'parrot',
  })
}

const roomStore = useRoomStore();
const hasHomeWord = ref(false)


watch(() => roomStore.status, async (val) => {
  if (val === 3) {
    hasHomeWord.value = await judgeHasHomeWork()
  }
})

function toHomeWork() {
  const url = `https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-personal-training/courses-detail.html?courseNo=${URLParams.roomNo}`
  webOpen({
    url,
    titleBar: true,
    noTopInset: false
  })
}

async function judgeHasHomeWork() {
  const {courseHomeworkBOList} = await getHomeWork()
  return courseHomeworkBOList?.length > 0
}

</script>

<style lang="scss">
.bg2 {
  background: url("../assets/image/bg2.png") no-repeat center center / 6.937rem auto;
}
</style>