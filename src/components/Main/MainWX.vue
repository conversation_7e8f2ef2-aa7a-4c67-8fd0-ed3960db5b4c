<template>
  <v-app>
    <div>
      <v-skeleton-loader
          class="mx-auto"
          type="image, list-item@4"
        >
      </v-skeleton-loader>
    </div>
    <v-row align="center" justify="space-around" class="px-4" no-gutters>
      <v-col cols="auto">
        <v-btn class="bg-mc-bule-darken-4 rounded-pill opacity-70" :height="44" variant="text" block @click="keepStay">
          当前页面继续使用
        </v-btn>
      </v-col>
      <v-col cols="auto">
        <div class="position-relative">
          <v-btn class="bg-mc-bule-lighten-1 rounded-pill" :height="44" variant="text" block>
            在驾考宝典中打开
          </v-btn>
          <wakeupBtn :targetUrl="targetUrl" />
        </div>
      </v-col>
    </v-row>
  </v-app>
</template>

<script lang='ts' setup>
import {ref} from 'vue'
import WakeupBtn from '@/components/WakeupBtn.vue';
import eventBus from '@/hooks/useMitt';

window.wxActive.wxConfig('wxc5790296aba601d0')

const targetUrl = ref(`http://jiakao.nav.mucang.cn/vip/new-vip?page=${encodeURIComponent(location.href)}`)

function keepStay() {
  eventBus.emit('send-outside-keep-stay')
}
</script>
