<template>
  <v-app class="bg-mc-bule-darken-1">
    <v-app-bar class="bg-mc-bule-darken-2" flat :height="72">
      <v-row no-gutters align="center" class="mx-auto" style="max-width: 72%; min-width: 1200px;">
        <v-col>
          <NetworkInfo v-if="roomStore.status === 2" />
        </v-col>
        <v-col cols="auto" style="font-size: 1.375rem;">
          <div class="d-flex justify-center align-center">
            {{ basicStore.subject }}
            <v-chip
              v-if="roomStore.status === 3"
              color="grey"
              variant="outlined"
              label density="compact"
              class="ml-2"
            >
              已结束
            </v-chip>
            <v-chip
              v-else-if="roomStore.status === 2"
              color="mc-green-lighten-1"
              variant="outlined"
              label density="comfortable"
              class="ml-2 px-2"
            >
              <v-icon>
                <img src="../../assets/svg/equalizer.svg" />
              </v-icon>
              直播中
            </v-chip>
            <v-chip
              v-else
              color="grey"
              variant="outlined"
              label density="compact"
              class="ml-2"
            >
              未开始
            </v-chip>
          </div>
        </v-col>
        <v-col align="end">
          <v-btn prepend-icon="mdi-monitor" v-if="(roomStore.isMaster || roomStore.isSauListener) && roomStore.status === 2" @click="changeVideoVisible">画面监控</v-btn>
          <v-dialog v-if="roomStore.status === 1 && false" :max-width="460" persistent>
            <template v-slot:activator="{ props }">
              <v-btn prepend-icon="mdi-access-point" v-bind="props">设备检测</v-btn>
            </template>
            <template v-slot:default="{ isActive }">
                <div class="bg-mc-bule-darken-7 pa-4">
                  <Device @closeSelf="isActive.value = false" />
                </div>
            </template>
          </v-dialog>
        </v-col>
      </v-row>
    </v-app-bar>
    <v-main class="position-relative" style="height: 0;" id="main-target">
      <template v-if="roomStore.isMaster || roomStore.isSauListener">
        <Board v-if="roomStore.status !== 3 && boardStore.isSdkReady" />
      </template>
      <CourseContent v-else />
      <div
        class="position-absolute d-flex flex-column"
        style="height: 180px; width: 360px; margin-bottom: 56px;"
        :style="[{left: `${left}px`, bottom: `${bottom}px`}, !messageVisible ? {opacity: 0, 'pointer-events': 'none'} : {}]">
        <MessageListH5 v-if="chatStore.isTimReady" @messageScrollMove="showMessage" />
      </div>
      <div class="position-absolute video-right-side" v-if="videoVisible">
        <div :style="{width: `${240*videoEnlarge}px`}" class="bg-mc-bule-darken-1 position-absolute right-0 top-0">
          <v-row no-gutters justify="space-between">
            <v-col cols="auto">
              <v-btn @click="reloadH5Element" v-if="roomStore.isMaster">同步</v-btn>
            </v-col>
            <v-col align="end">
              <v-btn @click="changeVideoVisible">关闭</v-btn>
              <v-btn class="text-lowercase" @click="changeVideoEnlarge">放大 x{{ videoEnlarge }}</v-btn>
            </v-col>
          </v-row>
          <div>
            <Player v-if="roomStore.whStream" :stream="roomStore.whStream" />
            <div style="padding-bottom: 56.25%;" v-else class="bg-mc-bule-darken-6"></div>
          </div>
        </div>
      </div>
    </v-main>
    <v-footer app class="bg-mc-bule-darken-2" :height="104">
      <FooterComp @enterRoom="handleEnter(true)" @exitRoom="handleExit"></FooterComp>
    </v-footer>
  </v-app>
</template>

<script lang='ts' setup>
import { onMounted, watch, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { getSdk } from '@/services/board';
import { TEduBoard, boardInstance } from '@/services/board';
import Board from '@/components/Board/Main.vue';
import FooterComp from '@/components/Footer.vue';
import CourseContent from '@/components/CourseContent.vue';
import MessageListH5 from '@/components/MessageList/MessageListH5.vue';
import NetworkInfo from '@/components/NetworkInfo.vue';
import Device from '@/components/Device.vue';
import useBasicStore from '@/store/basic';
import useRoomStore from '@/store/room';
import useBoardStore from '@/store/board';
import useChatStore from '@/store/chat';
import Player from '@/components/Player.vue';
import useAuth from '@/hooks/useAuth';
import userRoomActionManager from '@/hooks/userRoomActionManager';
import useDeviceManager from '@/hooks/useDeviceManager';
import useResizeWatch from '@/hooks/useResizeWatch';
import { trackEvent } from '@/utils/utils';

const basicStore = useBasicStore();
const roomStore = useRoomStore();
const boardStore = useBoardStore();
const chatStore = useChatStore();
useAuth()
const {handleEnter, handleExit} = userRoomActionManager()
const {initMediaDeviceList, initCommand} = useDeviceManager()
const { left, bottom } = useResizeWatch()
let videoVisible = ref(false)
let videoEnlarge = ref(1)
const { messageList } = storeToRefs(chatStore);
const messageVisible = ref(false)
let timer: NodeJS.Timeout

watch(() => roomStore.status, async (val) => {
  if (val !== 3) {
    initMediaDeviceList()
    initCommand()
  }
}, {
  once: true
})

watch(messageList, () => {
  if (messageList.value.length) {
    showMessage()
  }
})


function changeVideoVisible() {
  videoVisible.value = !videoVisible.value
}

function changeVideoEnlarge() {
  if (videoEnlarge.value > 2) {
    videoEnlarge.value = 1
  } else {
    videoEnlarge.value += 0.5
  }
}
function reloadH5Element() {
  const el = boardInstance.getBoardElementList() as {type: number, elementId: string}[]
  const elements = el.filter(item => item.type === 2)
  if (elements?.length) {
    boardInstance.setElementsDisplay([TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_H5], elements.map(item => item.elementId), false);
    setTimeout(() => {
      boardInstance.setElementsDisplay([TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_H5], elements.map(item => item.elementId), true);
    }, 1000)
  }

  trackEvent({
    fragmentName1: '画面监控',
    actionType: '点击',
    actionName: '同步',
  })
}

onMounted(() => {
  getSdk()
})

function showMessage() {
  messageVisible.value = true
  clearTimeout(timer)
  timer = setTimeout(() => {
    messageVisible.value = false
  }, 5000)
}

</script>

<style lang="scss">
.video-right-side {
  top: var(--v-layout-top) !important;
  right: 0;
}
</style>