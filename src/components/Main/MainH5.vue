<template>
  <v-app class="line-bg1" @click="showToolbar">
    <HeaderComp v-if="isMucang" />
    <div :style="{height: isLandscape ? '100vh':'56.25vw'}" class="bg-mc-bule-darken-9">
      <CourseContent />
    </div>
    <v-main v-if="chatStore.isTimReady" @touchstart="showToolbar" @touchmove="showToolbar" @touchend="showToolbar" class="d-flex flex-column flex-1-1">
      <div v-if="isLandscape" :style="!(toolbarVisible || basicStore.isInputing) ? {opacity: 0, 'pointer-events': 'none'} : {}" >
        <div class="position-absolute left-0 top-0 pt-4 pl-4">
          <FullScreenControl icon="mdi-arrow-left"></FullScreenControl>
        </div>
        <v-card
          :style="{
            top: 'auto',
            bottom: 0,
            height: '50%',
            width: '60%',
            position: 'absolute'
          }" class="d-flex flex-column bg-transparent" variant="flat" height="100%">
          <MessageList class="safe-box-left"></MessageList>
          <div class="safe-box">
            <v-row no-gutters align-content="center" class="pa-4 pt-11 position-relative">
              <v-col cols="auto" class="pr-2">
                <AudioControl />
              </v-col>
              <v-col class="flex-grow-1">
                <ChatEditor></ChatEditor>
              </v-col>
            </v-row>
          </div>
        </v-card>
        <div class="position-absolute right-0 bottom-0 pb-4 pr-4">
          <FullScreenControl></FullScreenControl>
        </div>
      </div>
      <v-card 
        v-if="!isLandscape" class="d-flex flex-column bg-transparent flex-1-1" variant="flat">
        <MessageList></MessageList>
        <div class="safe-box">
          <v-row no-gutters align-content="center" class="pa-4 pt-11 position-relative">
            <v-col cols="auto" class="pr-2">
              <AudioControl />
            </v-col>
            <v-col class="flex-grow-1">
              <ChatEditor></ChatEditor>
            </v-col>
          </v-row>
        </div>
      </v-card>
    </v-main>
    <v-dialog v-model="examDialogVisible" :width="'100%'" :max-width="'100%'" height="500" content-class="bottom-0 ma-0" persistent>
      <ExamDialog @closeSelf="closeDialog" />
    </v-dialog>
  </v-app>
</template>

<script lang='ts' setup>
import {MCProtocol} from '@simplex/simple-base'
import { onMounted, ref, watch } from 'vue';
import MessageList from '@/components/MessageList';
import ChatEditor from '@/components/ChatEditor';
import useBasicStore from '@/store/basic';
import useRoomStore from '@/store/room';
import useChatStore from '@/store/chat';
import CourseContent from '@/components/CourseContent.vue';
import HeaderComp from '@/components/Header.vue';
import useAuth from '@/hooks/useAuth';
import userRoomActionManager from '@/hooks/userRoomActionManager';
import useDeviceManager from '@/hooks/useDeviceManager';
import useOrientationWatch from '@/hooks/useOrientationWatch';
import AudioControl from '@/components/AudioControl.vue';
import FullScreenControl from '@/components/FullScreenControl.vue';
import ExamDialog from '@/components/ExamDialog.vue';
import { URLParams, isMucang } from '@/utils/utils';
import useExamStatus from '@/hooks/useExamStatus';

const basicStore = useBasicStore();
const roomStore = useRoomStore();
const chatStore = useChatStore();
let examDialogVisible = ref(false)
useAuth()
userRoomActionManager()
const {initMediaDeviceList, initCommand} = useDeviceManager()
const {isLandscape} = useOrientationWatch()
const {onStartExam, showExamDialog} = useExamStatus()
const toolbarVisible = ref(false)
let timer: NodeJS.Timeout

watch(() => roomStore.status, async (val) => {
  if (val !== 3) {
    initMediaDeviceList()
    initCommand()
  }
}, {
  once: true
})

onMounted(() => {  
  const result = showExamDialog(URLParams.courseId)
  if (result) {
    examDialogVisible.value = true
  } else {
    onStartExam(() => {
      MCProtocol.Core.Web.setting({
        orientation: 'portrait',
        noTopInset: true,
        title: '直播',
        menu: false,
        titleBar: false,
      });
      examDialogVisible.value = true
    })
  }
})

function closeDialog() {
  examDialogVisible.value = false
}

function showToolbar() {
  toolbarVisible.value = true
  clearTimeout(timer)
  timer = setTimeout(() => {
    toolbarVisible.value = false
  }, 3500)
}

</script>

<style lang="scss">
.safe-box {
  padding-bottom: calc(constant(safe-area-inset-bottom) - 10px);
  padding-bottom: calc(env(safe-area-inset-bottom) - 10px);
}
.safe-box-left {
  margin-bottom: calc(constant(safe-area-inset-left) - 20px) !important;
  margin-left: calc(env(safe-area-inset-left) - 20px) !important;
}
.line-bg1 {
  background: linear-gradient(180deg,#142F5B, #1B3D6F) !important;
}
</style>