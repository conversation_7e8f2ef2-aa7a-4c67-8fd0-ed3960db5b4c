<template>
  <v-card height="100%" class="rounded-0" theme="light" v-if="!showResult">
    <v-toolbar class="bg-white" density="compact" style="border-bottom: 1px solid #F4F4F4;">
      <div class="pl-4">随堂测验 <span v-if="questionList.length">({{ currentQuestionIndex + 1 }}/{{ questionList.length }})</span></div>
      <v-spacer></v-spacer>
      <!-- <v-btn icon="mdi-close" variant="text" size="small" density="comfortable"></v-btn> -->
    </v-toolbar>
    <template v-if="questionItem.qId">
      <div class="swiper-slide pa-3" :data-qId="questionItem.qId">
        <div class="top">
          <span class="topic-type">{{ questionItem.showOptionType }}</span>
          <span class="topic-title">{{ questionItem.title }}</span>
        </div>

        <div class="show" v-if="questionItem.mediaType !== 0">
          <img v-if="questionItem.mediaType === 1" width="300" :src="questionItem.mediaContent" />
          <video v-else id="video" :src="questionItem.mediaContent" autoplay controls></video>
        </div>

        <div class="select-answer">
          <div class="answer-list">
            <template v-for="item in questionItem.options">
              <!-- 只要选中并且不是背题模式并且没有做题就给类名select  只要答案是正确的并且做题了并且选择答案的选项没有就给类名rightSelect，如果题目做了并且选的当前选项是错误的并且不是背题模式就给类名showWrong，答案是正确的并且展示答案或者选择的选项中有这个 -->
              <!-- 只要是背题模式就肯定是showRight -->
              <div
                v-if="item.showKey"
                :class="{
                  select: questionItem.userSelect[item.showKey] && questionItem.isCorrent == 1,
                  rightSelect: item.correct && questionItem.isCorrent != 1 && !questionItem.userSelect[item.showKey],
                  showWrong: questionItem.userSelect[item.showKey] && questionItem.isCorrent != 1 && !item.correct,
                  showRight: item.correct && questionItem.userSelect[item.showKey] && questionItem.isCorrent != 1,
                }"
                class="answer-item"
                @click="onSelectAnswer(item.showKey, item.value, item.correct ? 1 : 0)"
              >
                <span class="item-icon">{{ item.showKey }}</span>
                <span class="text">{{ item.value }}</span>
              </div>
            </template>
          </div>
        </div>
      </div>
      <v-card-actions class="pa-4">
        <v-btn class="bg-mc-bule-lighten-1 rounded-pill" :height="44" variant="text" block @click="onNext">
          下一题
        </v-btn>
      </v-card-actions>
    </template>
  </v-card>
  <v-card v-else height="100%" class="rounded-0 flex-grow-1" theme="light">
    <v-toolbar class="bg-white" density="compact" style="border-bottom: 1px solid #F4F4F4;">
      <div class="pl-4">随堂测验</div>
      <v-spacer></v-spacer>
      <v-btn icon="mdi-close" variant="text" size="small" density="comfortable" @click="emit('closeSelf')"></v-btn>
    </v-toolbar>
    <v-card-text>
      <div class="bg1">测验题已全部答完</div>
      <v-table density="compact" class="bg-transparent text-center mt-4">
        <thead>
          <tr style="background-color: #f5f7fa;">
            <th style="border: 1px solid #f5f7fa" class="text-center">答对</th>
            <th style="border: 1px solid #f5f7fa" class="text-center">答错</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="border: 1px solid #f5f7fa">{{ result.rightqIds.length }}</td>
            <td style="border: 1px solid #f5f7fa">{{ result.errorqIds.length }}</td>
          </tr>
        </tbody>
      </v-table>
    </v-card-text>
    <template v-if="hasEvaluateJob">
      <div class="text-center text-subtitle-2 px-5" style="color: #a0a0a0 ;">
        本节课的上课感受如何？点击下方按钮写下您的真实感受，这对接下来的课程安排非常重要。
      </div>
      <v-card-actions class="px-4 pb-4">
        <v-btn class="bg-mc-bule-lighten-1 rounded-pill" :height="44" variant="text" block @click="goEvaluate">
          填写本节课上课感受
        </v-btn>
      </v-card-actions>
    </template>
  </v-card>
</template>
<script lang='ts' setup>
import { httpRequest, URLParams, webOpen, trackEvent } from '@/utils/utils';
import { questionFn, QuestionMap, Question } from '@/utils/question';
import { computed, onMounted, ref, getCurrentInstance, watch } from 'vue';
import TUIMessage from '@/components/TUI/Message/index';
import useExamStatus from '@/hooks/useExamStatus';

async function getTopicDetail(qIdList: number[]): Promise<QuestionMap[]> {
  return httpRequest({
    host: 'tiku',
    url: 'api/web/feedback/banner.htm',
    method: 'post',
    jsonForm: true,
    params: {
      ids: qIdList.join(',')
    },
  }).then((data: {itemList: Question[]}) => {
    let questionList: QuestionMap[] = [];

    qIdList.forEach((id: number) => {
      let itemData: Question = { questionId: id, question: '题库精简升级，该试题已移除，无需继续练习。请练习其它试题，祝顺利拿本!', deleted: true } as Question;
      data.itemList.forEach((item: any) => {
        if (+id === item.questionId) {
          itemData = item;
        }
      });

      questionList.push(questionFn(itemData));
    });
    return questionList
  })
};

async function getExamQuestionIds() {
  return await httpRequest({
    url: 'api/web/course/get-course-examine-questions.htm',
    params: {
      courseId: URLParams.courseId,
    },
    host: 'parrot',
  })
}

async function submitExamRecord(data: {
  examineEncodeId: string,
    errorqIds: string,
    rightqIds: string,
    answers: string,
}) {
  return await httpRequest({
    host: 'parrot',
    url: 'api/web/pts-lesson-examine/submit-answers.htm',
    method: "post",
    params: data
  })
}

async function needComment() {
  // todo 判断是否需要评价按钮
  return await httpRequest({
    url: 'api/web/course/need-submit-comment.htm',
    params: {
      courseId: URLParams.courseId,
    },
    host: 'parrot',
  })
}

const { appContext } = getCurrentInstance()!
const {sendSubmitExam} = useExamStatus()
const emit = defineEmits(['closeSelf']);
let questionIdList: number[] = [];
let contentId = ''

const questionList = ref<QuestionMap[]>([]);

const currentQuestionIndex = ref(0);
const hasEvaluateJob = ref(false)

const questionItem = computed(() => {
  return questionList.value[currentQuestionIndex.value] || {};
});

const result = computed(() => {
  let rightqIds: number[] = [];
  let errorqIds: number[] = [];
  let answers: number[] = [];

  questionList.value.forEach((item) => {
    if (item.isCorrent === 2) {
      rightqIds.push(item.qId);
    }
    if (item.isCorrent === 3) {
      errorqIds.push(item.qId);
    }

    answers.push(item.getUserAnser());
  });

  return {
    rightqIds,
    errorqIds,
    answers,
  };
});

const showResult = computed(() => {
  return result.value.rightqIds.length + result.value.errorqIds.length === questionIdList.length;
});

watch(showResult, async (val) => {
  if (val) {
    hasEvaluateJob.value = result.value.errorqIds.length / result.value.answers.length >= 0.5 || await judgeHasEvaluateJob()
  }
}, {
  immediate: true
})

const getQuestionDetail = async () => {
  let saveExamInfo = JSON.parse(localStorage.getItem(`examInfo-${URLParams.courseId}`) || '{}');
  if (saveExamInfo.questionList?.length) {
    questionList.value = saveExamInfo.questionList.map((item: any) => {
      return questionFn(item, true);
    });

    currentQuestionIndex.value = Math.min(result.value.rightqIds.length + result.value.errorqIds.length, questionIdList.length - 1);
  } else {
    questionList.value = await getTopicDetail(questionIdList);
    localStorage.setItem(
      `examInfo-${URLParams.courseId}`,
      JSON.stringify({
        questionList: questionList.value,
      })
    );
  }
};

const getQuestionList = async () => {
  const {questionIds, examineEncodeId} = await getExamQuestionIds()
  questionIdList = questionIds
  contentId = examineEncodeId
  getQuestionDetail();
};

onMounted(() => {
  getQuestionList();
});

const nextQuestion = () => {
  currentQuestionIndex.value = (currentQuestionIndex.value as number) + 1;
};

const onSubmitExam = async () => {
  await submitExamRecord({
    examineEncodeId: contentId as string,
    errorqIds: result.value.errorqIds.join(','),
    rightqIds: result.value.rightqIds.join(','),
    answers: result.value.answers.join(','),
  });
  sendSubmitExam()
  trackEvent({
    actionType: '提交',
    actionName: '随堂测验做题数据',
    content: JSON.stringify({
      examineEncodeId: contentId as string,
      errorqIds: result.value.errorqIds.join(','),
      rightqIds: result.value.rightqIds.join(','),
      answers: result.value.answers.join(','),
    }),
  });
};

const finishedSelectAnswer = (selectRight: boolean, correctOptions: QuestionMap['options']) => {
  TUIMessage({
    type: 'info',
    message: selectRight ? '回答正确' : `答错了，正确答案${correctOptions.map(item => item.showKey).join(',')}`,
    duration: 2000,
    appContext
  })
  // 存储题目
  localStorage.setItem(
    `examInfo-${URLParams.courseId}`,
    JSON.stringify({
      questionList: questionList.value,
    })
  );

  if (currentQuestionIndex.value === questionList.value.length - 1) {
    onSubmitExam();
    return;
  }

  setTimeout(() => {
    nextQuestion();
  }, 2000);
};

const onSelectAnswer = (showKey: string, value: string, correct: number) => {
  const userSelect = {
    showKey,
    value,
    correct,
  };

  // 作答过的题目或者展示答案的时候不能作答
  if (questionItem.value.isCorrent !== 1) {
    return false;
  }

  // 单选题现在就计算答案(多选就return);
  if (questionItem.value.optionType <= 1) {
    // 将选择的结果赋值给当前数组对应的数据
    questionItem.value.userSelect = {
      [userSelect.showKey]: userSelect,
    };
  } else {
    if (questionItem.value.userSelect[userSelect.showKey]) {
      // 多选的时候如果多次点击相同一个选项，第二次点击就是取消
      delete questionItem.value.userSelect[userSelect.showKey];
    } else {
      questionItem.value.userSelect[userSelect.showKey] = userSelect;
    }
  }
};

const onCompareAnswer = () => {
  const userSelect = questionItem.value.userSelect;
  let selectRight = questionItem.value.StrCorrect === Object.keys(questionItem.value.userSelect).sort().join(',');

  if (questionItem.value.isCorrent !== 1) {
    return false;
  }

  if (questionItem.value.optionType <= 1) {
    // 单选题
    if (Object.keys(userSelect).length !== 1) {
      TUIMessage({
        type: 'info',
        message: '请至少选择一个选项',
        duration: 3000,
        appContext
      })
      return false;
    }
    questionItem.value.isCorrent = selectRight ? 2 : 3;
  } else {
    // 多选题
    if (Object.keys(userSelect).length < 2) {
      TUIMessage({
        type: 'info',
        message: '请选择2个及以上答案',
        duration: 3000,
        appContext
      })
      return false;
    }

    questionItem.value.isCorrent = selectRight ? 2 : 3;
  }

  finishedSelectAnswer(selectRight, questionItem.value.correctOptions);
};

const onNext = () => {
  if(questionItem.value.question.deleted){
    nextQuestion();
  }else{
    onCompareAnswer();
  }
  
};

async function judgeHasEvaluateJob() {
  const {value} = await needComment()
  return value
}

function goEvaluate() {
  const url = `https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-personal-training/subject-comment.html?courseNo=${URLParams.roomNo}`
  webOpen({
    url,
    titleBar: true,
    noTopInset: false
  })
}
</script>

<style lang="scss">
.bg1 {
  background: url("../assets/image/bg1.png") no-repeat center center / 7rem auto;
  padding-top: 7rem;
  text-align: center;
}
</style>
<style lang="scss">
.swiper-slide {
  flex: 1;
  overflow: auto;
  text-align: left;
  .image-box {
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
  }

  img {
    width: auto;
    max-width: 100%;
    max-height: 200px;
  }

  .top {
    .topic-type {
      display: inline-block;
      height: 18px;
      line-height: 18px;
      text-align: center;
      background-color: #1dacf9;
      color: white;
      font-size: 10px;
      border-radius: 5px 5px 5px 0;
      padding: 0 8px;
      vertical-align: top;
      margin-right: 2px;
      margin-top: 4px;
      transform: scale(0.9);
    }

    .topic-title {
      font-size: 18px;
      line-height: 25px;
      color: #333;
    }
  }

  .show {
    margin-top: 15px;
    padding: 0 15px;
    box-sizing: border-box;
    text-align: center;

    image,
    video {
      max-width: 100%;
    }
  }

  .select-answer {
    margin-top: 13px;

    .answer-list {
      .answer-item {
        cursor: pointer;
        box-sizing: border-box;
        min-height: 52px;
        padding: 7px 15px;
        display: flex;
        align-items: center;
        color: #333;

        .item-icon {
          width: 28px;
          height: 28px;
          box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.15);
          border: 1px solid rgba(0, 0, 0, 0.05);
          border-radius: 50%;
          font-size: 16px;
          text-align: center;
          line-height: 28px;
          margin-right: 15px;
          flex-shrink: 0;
        }

        .text {
          font-size: 18px;
        }

        /* 多选选中的选项 */
        &.select {
          .item-icon {
            border: none;
            background-color: #b0b7c6;
            color: white;
          }
        }

        /* 正确的选项 */
        &.rightSelect:not(.select) {
          color: #1dacf9;

          .item-icon {
            background-color: #1dacf9;
            color: white;
          }
        }

        /* 答案选择错误 */
        &.showWrong {
          .item-icon {
            position: relative;

            &:after {
              content: '';
              position: absolute;
              width: 38px;
              height: 38px;
              background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/jkbd-ic-dati-wrong.png) no-repeat center center/cover;
              top: -5px;
              left: -5px;
            }
          }

          .text {
            color: #ff4a40;
          }
        }

        /* 答案选择正确 */
        &.showRight {
          .item-icon {
            position: relative;

            &:after {
              content: '';
              position: absolute;
              width: 38px;
              height: 38px;
              background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/jkbd-ic-dati-right.png) no-repeat center center/cover;
              top: -5px;
              left: -5px;
            }
          }

          .text {
            color: #1dacf9 !important;
          }
        }
      }

      .submit-answer {
        height: 44px;
        margin: 72px auto 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(90deg, rgba(0, 224, 229, 1) 0%, rgba(0, 134, 250, 1) 100%);
        border-radius: 22px;
        color: white;
        font-size: 17px;
      }
    }
  }
}
</style>
