<template>
  <div class="position-absolute" style="left: 0; bottom: 4rem">
    <v-btn
      class="bg-mc-bule-darken-3 opacity-80 ml-4 rounded-pill"
      @click="sendMessage(item)"
      variant="text" size="small" :width="32" v-for="item in quickList">
      {{ item }}
    </v-btn>
  </div>
  <div class="d-flex pr-2 align-center rounded-pill" style="background-color: rgba(0,0,0,0.45);">
    <v-text-field v-model="text" density="default" bg-color="transparent" hide-details variant="solo" flat :disabled="isMessageDisabled" :placeholder="isMessageDisabled ? '已被主持人禁言' : '说点什么...'" @update:focused="focused"></v-text-field>
    <v-btn class="ml-2" icon size="small" density="comfortable" @click="sendMessage()" variant="text">
      <v-icon>mdi-send-outline</v-icon>
    </v-btn>
  </div>
</template>

<script lang='ts' setup>
import {ref} from 'vue'
import { storeToRefs } from 'pinia';
import useChatEditor from '@/hooks/useChatEditor';
import useBasicStore from '@/store/basic';

const basicStore = useBasicStore();
const { isInputing } = storeToRefs(basicStore);

function focused(r: boolean) {
  if (!r) {
    setTimeout(() => {
      isInputing.value = r
    }, 300)
    return
  }
  isInputing.value = r
}
const quickList = ref(['A', 'B', 'C', 'D'])

const {
  text,
  isMessageDisabled,
  sendMessage,
} = useChatEditor();

</script>
