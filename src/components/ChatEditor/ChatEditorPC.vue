<template>
  <v-toolbar density="compact" class="px-4 py-2" style="border-top: 1px solid #18184d;">
    <v-text-field v-model="text" density="default" bg-color="mc-bule-darken-4" hide-details variant="solo" flat :disabled="isMessageDisabled" :placeholder="isMessageDisabled ? '已被主持人禁言' : '说点什么...'"></v-text-field>
    <v-btn class="bg-mc-bule-lighten-1 ml-2" @click="sendMessage()" variant="text">
      发送
    </v-btn>
  </v-toolbar>
</template>

<script lang='ts' setup>
import useChatEditor from '@/hooks/useChatEditor';

const {
  text,
  isMessageDisabled,
  sendMessage,
} = useChatEditor();

</script>
