<template>
  <v-list v-model:selected="selectedList" density="compact" variant="text" class="bg-transparent pa-0">
    <v-list-item height="60" class="mx-4 mt-6 bg-mc-bule-darken-4" :value="1">
      <template v-slot:append>
        <v-icon icon="mdi-chevron-right"></v-icon>
      </template>
      <v-list-item-title>专项课件</v-list-item-title>
    </v-list-item>
    <v-list-item height="60" class="mx-4 mt-6 bg-mc-bule-darken-4" :value="2">
      <template v-slot:append>
        <v-icon icon="mdi-chevron-right"></v-icon>
      </template>
      <v-list-item-title>临考冲刺课件</v-list-item-title>
    </v-list-item>
    <v-list-item height="60" class="mx-4 mt-6 bg-mc-bule-darken-4" :value="3">
      <template v-slot:append>
        <v-icon icon="mdi-chevron-right"></v-icon>
      </template>
      <v-list-item-title>考前两小时题库</v-list-item-title>
    </v-list-item>
  </v-list>
  <v-slide-x-reverse-transition>
    <v-card
      v-if="selectedList.length"
      class="d-flex flex-column bg-mc-bule-darken-7 position-absolute w-100"
      height="100%"
      style="bottom: 0;"
    >
      <v-toolbar density="compact" class="text-mc-gray-lighten-1 bg-mc-bule-darken-8">
        <v-btn icon="mdi-chevron-left" variant="text" size="small" @click="selectedList = []"></v-btn>
        <v-spacer></v-spacer>
      </v-toolbar>
      <div class="flex-grow-1 overflow-y-scroll">
        <v-list class="bg-transparent" v-if="selectedList?.indexOf(1) > -1">
          <v-list-item
            v-for="item in coursewareList.filter(item => item.type === 1)"
            :key="item.id"
            link
            @click="onGoTeachPlan(item.type, item.bizId)"
          >
            <v-list-item-title>{{item.name}}</v-list-item-title>
          </v-list-item>
        </v-list>
        <v-list class="bg-transparent" v-if="selectedList?.indexOf(2) > -1">
          <v-list-item
            v-for="item in coursewareList.filter(item => item.type === 2)"
            :key="item.id"
            link
            @click="onGoTeachPlan(item.type, item.id)"
          >
            <v-list-item-title>{{item.name}}</v-list-item-title>
          </v-list-item>
        </v-list>
        <v-list class="bg-transparent" v-if="selectedList?.indexOf(3) > -1">
          <v-list-item
            v-for="item in specialList"
            :key="item.id"
            link
            @click="onGoWrongList(WrongListType.SpecialPractice, item.id, item.name, item.specialSource)"
          >
            <v-list-item-title>{{item.name}}</v-list-item-title>
          </v-list-item>
        </v-list>
      </div>
    </v-card>
  </v-slide-x-reverse-transition>
</template>

<script lang='ts' setup>
import { ref, onMounted } from 'vue';
import { URLParams, getAuthToken, httpRequest, getHost, trackEvent, convertParamsStr } from '@/utils/utils';
import eventBus from '@/hooks/useMitt';
import {WrongListType, CoursewareType, WrongListSpecialPractice} from '@/utils/constant';

interface coursewareItem {
  bizId: number,
  name: string,
  type: CoursewareType,
  id: number,
}

const emit = defineEmits(['closeSelf']);
let selectedList = ref([] as number[])

const coursewareList = ref<coursewareItem[]>([]);
const specialList = ref<any[]>([]);

async function getCourseWareUrl(params: {coursewareType: number, id: number}) {
  const host = await getHost()
  const authToken = await getAuthToken()
  const courseId = URLParams.courseId
  return `${host}personal-training-management/?door=${authToken}#/${courseId}/main/teachPlan?${convertParamsStr(params)}`
}
async function getWrongListUrl(params: WrongListSpecialPractice) {
  const host = await getHost()
  const authToken = await getAuthToken()
  const courseId = URLParams.courseId
  return `${host}personal-training-management/?door=${authToken}#/${courseId}/main/wrongList?${convertParamsStr(params)}`
}

async function getCoursewareList() {
  return await httpRequest({
    url: 'api/web/courseware/get-course-list-by-type.htm',
    params: {
      courseId: URLParams.courseId,
    },
    host: 'parrot',
  })
}
async function getSpecialList() {
  return await httpRequest({
    url: 'api/web/course/get-special-list.htm',
    params: {
      courseId: URLParams.courseId,
    },
    host: 'parrot',
  })
}

onMounted(async () => {
  getCoursewareList().then((data) => {
    coursewareList.value = data.itemList;
  })
  getSpecialList().then((data) => {
    specialList.value = data.itemList
  })
})

function openTeachPlan(url: string) {
  if (!url) return
  eventBus.emit('tiw-add-element', url);
  emit('closeSelf')
}
async function onGoWrongList(type = WrongListType.SpecialPractice, id: number, name: string, specialSource: number) {
  let url = await getWrongListUrl({
    wrongListType: WrongListType.SpecialPractice,
    specialId: id,
    name: name,
    specialSource: specialSource,
  })

  openTeachPlan(url)

  trackEvent({
    fragmentName1: '资料库',
    actionType: '点击',
    actionName: '打开试题列表',
    url,
  })
}
async function onGoTeachPlan(type = CoursewareType.Special, id: number) {
  let url = await getCourseWareUrl({
    coursewareType: type,
    id,
  })
  openTeachPlan(url)
  
  let actionName = '打开专项课件'
  if (type === CoursewareType.Exam) {
    actionName = '打开考前冲刺课件'
  }

  trackEvent({
    fragmentName1: '教案',
    actionType: '点击',
    actionName,
    url,
  })
};
</script>
