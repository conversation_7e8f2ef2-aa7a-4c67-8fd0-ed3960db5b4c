import { createVNode, render, getCurrentInstance } from 'vue';

import TUIMessageBox from './index.vue';

export type MessageProps = {
    title: string,
    message: string,
    callback?: (resule?: boolean) => Promise<void>,
    confirmButtonText?: string,
    cancelButtonText?: string,
    appContext?: any,
    closeInMask?: boolean
}
const MessageBox = ({ title, message, callback, confirmButtonText, cancelButtonText, closeInMask, appContext }: MessageProps) => {
  const container = document.createElement('div');
  const fullscreenElement = document.fullscreenElement || document.body;
  if (!fullscreenElement) return;
  fullscreenElement.appendChild(container);

  const onRemove = () => {
    render(null, container);
    fullscreenElement.removeChild(container);
  };

  const vnode = createVNode(TUIMessageBox, {
    title,
    message,
    callback,
    confirmButtonText,
    cancelButtonText,
    remove: onRemove,
    closeInMask
  });
  vnode.appContext = appContext || getCurrentInstance()?.appContext
  render(vnode, container);
};

export default MessageBox;
