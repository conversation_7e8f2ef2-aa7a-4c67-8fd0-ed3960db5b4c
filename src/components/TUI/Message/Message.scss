.message {
  &-enter {
    &-from {
      transform: translate3d(0, -75px, 0);
      opacity: 0;
    }

    &-active {
      transition: all 0.3s;
    }

    &-to {
      transform: none;
      opacity: 1;
    }
  }
}

.t-message {
  width: 100%;
  display: flex;
  word-break: break-word;
  position: absolute;
  z-index: 9999;
  top: 6%;
  
  .t-message-text {
    vertical-align: middle;
    font-size: .875rem;
  }

  .t-message-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background-size: cover;
    margin-right: 4px;
    vertical-align: middle;
  }
}

.t-message.t-message-error {
  color: #E04343;
  background-color: #FFEBEE;
}
.t-message.t-message-success {
  color: #00B89C;
  background-color: #EBFFFA;
}
.t-message.t-message-warning {
  color: #E59753;
  background-color: #FFF4EB;
}
.t-message.t-message-info {
  color: #494F6A;
  background-color: #EBF2FF;
}
