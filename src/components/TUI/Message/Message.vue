<template>
    <div
      v-show="isShow" ref="messageRef" :class="['t-message']"
      :style="{
        top: props.top.value,
        zIndex: props.zIndex
      }"
    >
    <v-row justify="center" class=" ma-auto" style="max-width: 80%;">
      <v-col cols="auto">
        <v-alert class="py-2 pl-3 pr-3 bg-white" :icon="svgName" :color="colorName" variant="tonal" density="compact">
          <template #prepend>
            <v-icon class="mr-n3"></v-icon>
          </template>
          {{ props.message }}
        </v-alert>
      </v-col>
    </v-row>
    </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, PropType, defineProps, computed } from 'vue';

const success = 'mdi-check-circle-outline';
const error = 'mdi-close-circle-outline';
const warning = 'mdi-alert-circle-outline';
const info = 'mdi-information-outline';
const typeMap = {
  success: 'success',
  error: 'red-accent-4',
  warning: 'warning',
  info: 'grey-darken-1'
}

const props = defineProps({
  type: {
    type: String as PropType<'success' | 'error' | 'warning' | 'info'>,
    default: 'success',
  },
  message: {
    type: [String, Number],
    default: '',
  },
  duration: {
    type: Number,
    default: 3000,
  },
  remove: {
    type: Function,
    default: () => {},
  },
  top: {
    type: Object,
    default: () => {},
  },
  zIndex: {
    type: Number,
    default: 1000,
  },
});

const svgName = computed(() => {
  const svgMap = { success, error, warning, info };
  return svgMap[props.type] || info;
});
const colorName = computed(() => {
  return typeMap[props.type || info];
});

const messageRef = ref();
const isShow = ref(false);
onMounted(async () => {
  onOpen();
});

const timer = ref();
const onOpen = () => {
  isShow.value = true;
  timer.value && clearTimeout(timer.value);
  if (props.duration > 0) {
    timer.value = setTimeout(() => {
      onClose();
    }, props.duration);
  }
};
const onClose = () => {
  isShow.value = false;
  props.remove();
};
</script>

<style lang="scss" scoped>
@use "./Message.scss";
</style>
