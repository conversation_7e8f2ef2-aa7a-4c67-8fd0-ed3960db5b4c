<template>
  <div  :style="{'padding-top': statusBarHeightRef}" v-show="!isLandscape">
    <v-row class="bg-transparent flex-grow-0 px-4 py-2" align-content="center" no-gutters>
      <v-col cols="1">
        <v-btn
          density="compact"
          icon="mdi-arrow-left"
          variant="text"
          @click="back">
        </v-btn>
      </v-col>
      <v-col class="d-flex justify-center align-center text-truncate">直播</v-col>
      <v-col cols="1"></v-col>
    </v-row>
  </div>
</template>

<script lang='ts' setup>
import { onMounted, ref } from 'vue';
import {MCProtocol} from '@simplex/simple-base'
import useOrientationWatch from '@/hooks/useOrientationWatch';

const {isLandscape} = useOrientationWatch()
let statusBarHeightRef = ref('')

function back() {
  MCProtocol.Core.Web.close()
}

onMounted(() => {
  setTimeout(() => {
    MCProtocol.Core.Web.setStatusBarTheme({
      theme: 'light',
    });
  }, 500)
  MCProtocol.Core.System.env((data: {data: {statusBarHeight: string}}) => {
    let {statusBarHeight} = data.data
    if (statusBarHeight) {
      statusBarHeight = statusBarHeight + 'px'
    } else {
      statusBarHeight = 'env(safe-area-inset-top)'
    }
    statusBarHeightRef.value = statusBarHeight
  })
})
</script>