<template>
  <v-list density="compact" variant="text" class="bg-transparent pa-0">
    <template v-for="item in contentList">
      <v-list-item height="60" class="mx-4 mt-6 bg-mc-bule-darken-4" @click="onGoCategory(item.contentId)" v-if="item.type === TeachType.Wrong && item.displaySpecial" value="1">
        <template v-slot:append>
          <v-icon icon="mdi-chevron-right"></v-icon>
        </template>
        <v-list-item-title>讲错题</v-list-item-title>
      </v-list-item>
      <v-list-item height="60" class="mx-4 mt-6 bg-mc-bule-darken-4" @click="onGoWrongList(WrongListType.Knowledge, item.contentId)" v-else-if="item.type === TeachType.Wrong" value="1">
        <template v-slot:append>
          <v-icon icon="mdi-chevron-right"></v-icon>
        </template>
        <v-list-item-title>所有错题(近{{item.scope}}次模考)</v-list-item-title>
      </v-list-item>
      <v-list-group v-if="item.type === TeachType.Special && item.contentList?.length" value="2">
        <template v-slot:activator="{ props }">
          <v-list-item height="60" class="mx-4 mt-6 bg-mc-bule-darken-4" v-bind="props">
            <v-list-item-title>专项讲解</v-list-item-title>
          </v-list-item>
        </template>

        <v-list-item
          class="mx-4 bg-mc-bule-darken-4"
          append-icon="mdi-chevron-right"
          @click="onGoTeachPlan(CoursewareType.Special, sItem.id)"
          v-for="sItem in item.contentList"
        >{{ sItem.name }}</v-list-item>
      </v-list-group>
      <v-list-group v-if="item.type === TeachType.Sprint && item.contentList?.length" value="3">
        <template v-slot:activator="{ props }">
          <v-list-item height="60" class="mx-4 mt-6 bg-mc-bule-darken-4" v-bind="props">
            <v-list-item-title>考前冲刺</v-list-item-title>
          </v-list-item>
        </template>

        <v-list-item
          class="mx-4 bg-mc-bule-darken-4"
          append-icon="mdi-chevron-right"
          @click="onGoTeachPlan(CoursewareType.Exam, sItem.id)"
          v-for="sItem in item.contentList"
        >{{ sItem.name }}</v-list-item>
      </v-list-group>
      <v-list-item height="60" class="mx-4 mt-6 bg-mc-bule-darken-4" @click="onGoWrongList(WrongListType.Other, item.contentId)" v-if="item.type === TeachType.Knowledge" value="4">
        <template v-slot:append>
          <v-icon icon="mdi-chevron-right"></v-icon>
        </template>
        <v-list-item-title>知识点巩固</v-list-item-title>
      </v-list-item>
    </template>
  </v-list>

  <v-dialog
    v-if="roomStore.isMaster"
    v-model="planSelectDialog"
    fullscreen>
    <template v-slot:default="{ isActive }">
      <div style="width: 85%; height: 95%;" class="ma-auto position-relative">
        <IframeWrap :src="categoryUrl" @onMessage="onMessage"></IframeWrap>
        <v-btn class="position-absolute right-0" color="grey-darken-4" icon="mdi-close" variant="text" @click="isActive.value = false"></v-btn>
      </div>
    </template>
  </v-dialog>
</template>

<script lang='ts' setup>
import { ref, onMounted, getCurrentInstance } from 'vue';
import useRoomStore from '@/store/room';
import { URLParams, getAuthToken, httpRequest, getHost, trackEvent, convertParamsStr } from '@/utils/utils';
import TUIMessage from '@/components/TUI/Message/index';
import eventBus from '@/hooks/useMitt';
import IframeWrap from '@/components/IframeWrap.vue';
import {TeachType, WrongListType, CoursewareType, WrongListOther, WrongListKnowledge2} from '@/utils/constant';

interface HomeItem {
  type: TeachType
  contentId: number
  contentList?: {
      id: number,
      name: string
  }[]
  detailId: number,
  displaySpecial: boolean,
  scope: number
}

const { appContext } = getCurrentInstance()!
const emit = defineEmits(['closeSelf']);
const roomStore = useRoomStore();
let categoryUrl = ref('')
let planSelectDialog = ref(false)
const contentList = ref<HomeItem[]>([]);

async function getTeachPlanCategoryUrl(id: number) {
  const host = await getHost()
  const authToken = await getAuthToken()
  const courseId = URLParams.courseId
  return `${host}personal-training-management/?door=${authToken}#/${courseId}/main/category?contentId=${id}`
}
async function getCourseWareUrl(params: {coursewareType: number, id: number}) {
  const host = await getHost()
  const authToken = await getAuthToken()
  const courseId = URLParams.courseId
  return `${host}personal-training-management/?door=${authToken}#/${courseId}/main/teachPlan?${convertParamsStr(params)}`
}
async function getWrongListUrl(params: WrongListOther | WrongListKnowledge2) {
  const host = await getHost()
  const authToken = await getAuthToken()
  const courseId = URLParams.courseId
  return `${host}personal-training-management/?door=${authToken}#/${courseId}/main/wrongList?${convertParamsStr(params)}`
}

async function getListPlanContent() {
  return await httpRequest({
    url: 'api/web/pts-lesson-plan/list-plan-content.htm',
    params: {
      courseId: URLParams.courseId,
    },
    host: 'parrot',
  })
}

onMounted(async () => {
  getListPlanContent().then((data) => {
    contentList.value = data.itemList;
  }).catch(e => {
    if (e?.message) {
      TUIMessage({
        type: 'error',
        message: e?.message,
        duration: 3000,
        appContext
      })
      
      setTimeout(() => {
        emit('closeSelf')
      }, 1000)
    }
  })
})

function onMessage(e: MessageEvent) {
  let {type, data} = e.data
  if (type === 'openPage') {
    openTeachPlan(data.url)

    trackEvent({
      fragmentName1: '错题专项分类弹窗',
      actionType: '点击',
      actionName: '打开试题列表',
      url: data.url,
    })
  }
}

function openTeachPlan(url: string) {
  if (!url) return
  eventBus.emit('tiw-add-element', url);
  emit('closeSelf')
}
async function onGoCategory(id: number) {
  let url = await getTeachPlanCategoryUrl(id)
  categoryUrl.value = url
  planSelectDialog.value = true
};
async function onGoWrongList(type = WrongListType.Other, id: number) {
  let url = ''
  if (type === WrongListType.Other) {
    url = await getWrongListUrl({
      wrongListType: WrongListType.Other,
      contentId: id,
    })
  } else if (type === WrongListType.Knowledge) {
    url = await getWrongListUrl({
      wrongListType: WrongListType.Knowledge,
      name: '所有错题',
      contentId: id,
    })
  }

  openTeachPlan(url)

  trackEvent({
    fragmentName1: '教案',
    actionType: '点击',
    actionName: '打开试题列表',
    url,
  })
}
async function onGoTeachPlan(type = CoursewareType.Special, id: number) {
  let url = await getCourseWareUrl({
    coursewareType: type,
    id,
  })
  openTeachPlan(url)
  
  let actionName = '打开专项课件'
  if (type === CoursewareType.Exam) {
    actionName = '打开考前冲刺课件'
  }

  trackEvent({
    fragmentName1: '教案',
    actionType: '点击',
    actionName,
    url,
  })
};
</script>
