<template>
  <v-col class="overflow-x-auto flex-grow-1 pa-0" id="message-list-selector">
    <v-row no-gutters
      class="px-4 pt-2 pb-1"
      v-for="item in messageList" :key="item.ID"
    >
      <template v-if="item.type === 'TRTCUserEnterMsg'">
        <v-col
        class="text-center"
        >
        <span style="color: #476486;">{{ item.nick || item.from }}</span>进入直播间
        </v-col>
      </template>
      <template v-else>
        <v-col
          cols="auto"
        >
          <v-avatar
            size="36px"
            :image="item.avatar"
          >
          </v-avatar>
        </v-col>
        <v-col
          class="text-left pl-3"
        >
          <div style="font-size: 0.812rem; color: #9FA3B2;">{{ item.nick || item.from }}</div>
          <div style="border-radius: 1px 6px 6px 6px; display: inline-block;" :style="{backgroundColor: item.flow === 'out' ? '#0a4282' : '#273352'}" class="mt-2 px-3 py-2">{{ item.payload.text }}</div>
        </v-col>
      </template>
    </v-row>
  </v-col>
</template>

<script lang='ts' setup>
import { onMounted, watch } from 'vue';
import useMessageList from '@/hooks/useMessageList';

const emit = defineEmits(['messageScrollMove']);

let isFirst = true

const {
  setMessageListInfo,
  messageList,
  scrollTop,
} = useMessageList('message-list-selector');

onMounted(async () => {
  if (isFirst) {
    setMessageListInfo();
    isFirst = false
  }
})

watch(scrollTop, (val) => {
  emit('messageScrollMove', val)
})

</script>
