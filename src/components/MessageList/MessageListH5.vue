<template>
  <v-col class="overflow-x-auto flex-grow-1 pa-0" id="message-list-selector-h5">
    <v-row no-gutters
      class="px-3 pt-2"
      v-for="item in messageList" :key="item.ID"
    >
      <div 
        v-if="item.type === 'TRTCUserEnterMsg'"
        class="text-left px-2 py-1"
        style="font-size: 1rem; border-radius: 12px; display: inline-block; background: rgba(0,0,0,0.50);" >
        <span style="color: #4FCD95;">{{ item.nick || item.from }}</span>进入直播间
      </div>
      <div
        v-else
        class="text-left px-2 py-1"
        style="font-size: 1rem; border-radius: 12px; display: inline-block; background: rgba(0,0,0,0.50);"
      >
        <span :style="{color: item.flow === 'out' ? '#EC85D8' : '#4FCD95'}">{{ item.nick || item.from }}：</span>
        <span>{{ item.payload.text }}</span>
      </div>
    </v-row>
  </v-col>
</template>

<script lang='ts' setup>
import { onMounted, watch } from 'vue';
import useMessageList from '@/hooks/useMessageList';

const emit = defineEmits(['messageScrollMove']);

let isFirst = true

const {
  setMessageListInfo,
  messageList,
  scrollTop,
} = useMessageList('message-list-selector-h5');

onMounted(async () => {
  if (isFirst) {
    setMessageListInfo();
    isFirst = false
  }
})

watch(scrollTop, (val) => {
  emit('messageScrollMove', val)
})

</script>
