<template>
  <v-card v-if="!showResult" height="100%" class="rounded-0 flex-grow-1 pa-0" theme="light">
    <v-toolbar class="bg-white" density="compact" style="border-bottom: 1px solid #F4F4F4;">
      <div class="pl-4">学员已完成随堂测验题，答题情况如下</div>
      <v-spacer></v-spacer>
      <v-btn icon="mdi-close" variant="text" size="small" density="comfortable" @click="emit('closeSelf')"></v-btn>
    </v-toolbar>
    <v-card-text>
      <v-table density="compact" class="bg-transparent text-center">
        <thead>
          <tr style="background-color: #f5f7fa;">
            <th style="border: 1px solid #f5f7fa" class="text-center">答对</th>
            <th style="border: 1px solid #f5f7fa" class="text-center">答错</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="border: 1px solid #f5f7fa">{{ result.rightqIds.length }}</td>
            <td style="border: 1px solid #f5f7fa">{{ result.errorqIds.length }}</td>
          </tr>
        </tbody>
      </v-table>
    </v-card-text>
    <v-card-actions class="justify-center pb-4">
      <v-btn class="bg-mc-bule-lighten-1" width="50%" :height="44" variant="text" @click="goRe">
        查看测验题
      </v-btn>
    </v-card-actions>
  </v-card>

  <v-card height="100%" class="rounded-0 pa-0" theme="light" v-else>
    <v-toolbar class="bg-white" density="compact" style="border-bottom: 1px solid #F4F4F4;">
      <div class="pl-4">随堂测验 <span v-if="questionList.length">({{ currentQuestionIndex + 1 }}/{{ questionList.length }})</span></div>
      <v-spacer></v-spacer>
      <v-btn icon="mdi-close" variant="text" size="small" density="comfortable" @click="emit('closeSelf')"></v-btn>
    </v-toolbar>
    <template v-if="questionItem.qId">
      <div class="swiper-slide pa-3" :data-qId="questionItem.qId">
        <div class="top">
          <span class="topic-type">{{ questionItem.showOptionType }}</span>
          <span class="topic-title">{{ questionItem.title }}</span>
        </div>

        <div class="show" v-if="questionItem.mediaType !== 0">
          <img v-if="questionItem.mediaType === 1" width="300" :src="questionItem.mediaContent" />
          <video v-else id="video" :src="questionItem.mediaContent" autoplay controls></video>
        </div>

        <div class="select-answer">
          <div class="answer-list">
            <template v-for="item in questionItem.options">
              <!-- 只要选中并且不是背题模式并且没有做题就给类名select  只要答案是正确的并且做题了并且选择答案的选项没有就给类名rightSelect，如果题目做了并且选的当前选项是错误的并且不是背题模式就给类名showWrong，答案是正确的并且展示答案或者选择的选项中有这个 -->
              <!-- 只要是背题模式就肯定是showRight -->
              <div
                v-if="item.showKey"
                :class="{
                  select: questionItem.userSelect[item.showKey] && questionItem.isCorrent == 1,
                  rightSelect: item.correct && questionItem.isCorrent != 1 && !questionItem.userSelect[item.showKey],
                  showWrong: questionItem.userSelect[item.showKey] && questionItem.isCorrent != 1 && !item.correct,
                  showRight: item.correct && questionItem.userSelect[item.showKey] && questionItem.isCorrent != 1,
                }"
                class="answer-item"
              >
                <span class="item-icon">{{ item.showKey }}</span>
                <span class="text">{{ item.value }}</span>
              </div>
            </template>
          </div>
        </div>
      </div>
      <v-card-actions class="pa-4">
        <v-btn @click="onPrev" :disabled="currentQuestionIndex === 0" class="bg-mc-bule-lighten-1" :height="44" width="48%" variant="text">上一题</v-btn>
        <v-btn @click="onNext" :disabled="currentQuestionIndex + 1 >= questionList.length" class="bg-mc-bule-lighten-1" :height="44" width="48%" variant="text">下一题</v-btn>
      </v-card-actions>
    </template>
  </v-card>
</template>
<script lang='ts' setup>
import { httpRequest, URLParams, getAuthToken, convertParamsStr, getHost } from '@/utils/utils';
import { questionFn, QuestionMap, Question } from '@/utils/question';
import { computed, onMounted, ref } from 'vue';
import eventBus from '@/hooks/useMitt';
import {WrongListType, WrongListCourseExamResult} from '@/utils/constant';

async function getTopicDetail(qIdList: number[]): Promise<QuestionMap[]> {
  return httpRequest({
    host: 'tiku',
    url: 'api/web/feedback/banner.htm',
    method: 'post',
    jsonForm: true,
    params: {
      ids: qIdList.join(',')
    },
  }).then((data: {itemList: Question[]}) => {
    let questionList: QuestionMap[] = [];

    qIdList.forEach((id: number) => {
      let itemData: Question = { questionId: id, question: '题库精简升级，该试题已移除，无需继续练习。请练习其它试题，祝顺利拿本!' } as Question;
      data.itemList.forEach((item: any) => {
        if (+id === item.questionId) {
          itemData = item;
        }
      });

      questionList.push(questionFn(itemData));
    });
    return questionList
  })
};

async function getExamQuestionIds() {
  return await httpRequest({
    url: 'api/web/course/get-course-examine-questions.htm',
    params: {
      courseId: URLParams.courseId,
    },
    host: 'parrot',
  })
}

async function getExamResult(id: string) {
  return await httpRequest({
    url: 'api/web/pts-lesson-examine/get-examine-info.htm',
    params: {
      examineEncodeId: id,
    },
    host: 'parrot',
  })
}

function openTeachPlan(url: string) {
  if (!url) return
  eventBus.emit('tiw-add-element', url);
  emit('closeSelf')
}

async function getWrongListUrl(params: WrongListCourseExamResult) {
  const host = await getHost()
  const authToken = await getAuthToken()
  const courseId = URLParams.courseId
  return `${host}personal-training-management/?door=${authToken}#/${courseId}/main/wrongList?${convertParamsStr(params)}`
}

const emit = defineEmits(['closeSelf']);
let showResult = ref(false)
let questionIdList: number[] = [];
let contentId = ''

const questionList = ref<QuestionMap[]>([]);

const currentQuestionIndex = ref(0);

const questionItem = computed(() => {
  return questionList.value[currentQuestionIndex.value] || {};
});

const result = computed(() => {
  let rightqIds: number[] = [];
  let errorqIds: number[] = [];
  let answers: number[] = [];

  questionList.value.forEach((item) => {
    if (item.isCorrent === 2) {
      rightqIds.push(item.qId);
    }
    if (item.isCorrent === 3) {
      errorqIds.push(item.qId);
    }

    answers.push(item.getUserAnser());
  });

  return {
    rightqIds,
    errorqIds,
    answers,
  };
});

const getQuestionDetail = async () => {
  questionList.value = await getTopicDetail(questionIdList);
};

const getQuestionList = async () => {
  const {questionIds, examineEncodeId} = await getExamQuestionIds()
  questionIdList = questionIds
  contentId = examineEncodeId
  await getQuestionDetail();
  getExamResultInfo()
};
const getExamResultInfo = async () => {
  const {answers} = await getExamResult(contentId) as {answers: number[]};

  const indexMap: Record<number, number> = { 16: 0, 32: 1, 64: 2, 128: 3 };
  answers.forEach((answer, index) => {
    const answerList = Object.keys(indexMap).map((item, index) => {
      return !!((16 << index) & answer) ? +item : 0
    }).filter(item => item)
    answerList.forEach(item => {
      const aIndex = indexMap[item]
      const userSelect = questionList.value[index].options[aIndex]

      onSelectAnswer(index, userSelect)
    })
    onCompareAnswer(index)
  })
}

const onSelectAnswer = (index: number, userSelect: QuestionMap['userSelect']) => {
  // 单选题现在就计算答案(多选就return);
  if (questionList.value[index].optionType <= 1) {
    // 将选择的结果赋值给当前数组对应的数据
    questionList.value[index].userSelect = {
      [userSelect.showKey]: userSelect,
    };
  } else {
    questionList.value[index].userSelect[userSelect.showKey] = userSelect;
  }
};
const onCompareAnswer = (index: number) => {
  let selectRight = questionList.value[index].StrCorrect === Object.keys(questionList.value[index].userSelect).sort().join(',');

  if (questionList.value[index].isCorrent !== 1) {
    return false;
  }

  if (questionList.value[index].optionType <= 1) {
    questionList.value[index].isCorrent = selectRight ? 2 : 3;
  } else {
    questionList.value[index].isCorrent = selectRight ? 2 : 3;
  }
};

onMounted(() => {
  getQuestionList();
});

const onPrev = () => {
  currentQuestionIndex.value = (currentQuestionIndex.value as number) - 1;
};
const onNext = () => {
  currentQuestionIndex.value = (currentQuestionIndex.value as number) + 1;
};

async function goRe() {
  const {examineEncodeId} = await getExamQuestionIds()
  const url = await getWrongListUrl({
    wrongListType: WrongListType.CourseExamResult,
    examineEncodeId,
  })
  openTeachPlan(url)
}
</script>
