<template>
  <v-row>
    <v-col
      cols="auto"
      style="flex-basis: 96px;"
    >
      麦克风
    </v-col>
    <v-col>
      <v-select
        :items="microphoneList"
        hide-details
        single-line
        :item-title="'label'"
        :item-value="'deviceId'"
        variant="outlined"
        v-model="currentMicrophoneId"
        :placeholder="microphoneList.length ? '选择麦克风设备' : '没有找到可用的麦克风设备'"
        :disabled="!microphoneList.length"
      ></v-select>
      <v-row align="center" class="mt-n1">
        <v-col
          cols="auto"
        >
          输出
        </v-col>
        <v-col class="d-flex justify-space-between">
          <div style="width: 4px; height: 12px; border-radius: 4px;" :class="volumeNum > index ? 'bg-blue-darken-3' : 'bg-grey-darken-1'" v-for="(item, index) in 24"></div>
        </v-col>
        <v-col
          cols="auto"
        >
        <v-btn color="success" density="compact" variant="text" v-if="isTest" @click="stopVolume">完成</v-btn>
        <v-btn color="info" density="compact" variant="text" v-else @click="testVolume">测试</v-btn>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
  <v-row>
    <v-col
      cols="auto"
      style="flex-basis: 96px;"
    >
    扬声器
    </v-col>
    <v-col>
      <v-select
        :items="speakerList"
        hide-details
        single-line
        :item-title="'label'"
        :item-value="'deviceId'"
        variant="outlined"
        v-model="currentSpeakerId"
        :disabled="!speakerList.length"
      ></v-select>
      <v-row align="center" class="mt-n1">
        <v-col
          cols="auto"
        >
          音频
        </v-col>
        <v-col>
          <audio controls style="width: 260px; height: 32px;" src="https://web.sdk.qcloud.com/trtc/electron/download/resources/media/TestSpeaker.mp3" controlsList="nodownload noplaybackrate"></audio>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
  <v-row>
    <v-col
      cols="auto"
      style="flex-basis: 96px;"
    >
    网络状况
    </v-col>
    <v-col align-self="center">
      <template v-if="average.RTT && !loading">
        <v-chip variant="text" class="pl-0" density="compact">
          <template #prepend>
            <v-icon color="success">mdi-wifi</v-icon>
          </template>
          {{average.RTT}}ms
        </v-chip>
        <v-chip variant="text" density="compact">
          <template #prepend>
            <v-icon color="info">mdi-arrow-up</v-icon>
          </template>
          {{CA[average.uplinkNetworkQuality]}}
        </v-chip>
        <v-chip variant="text" density="compact">
          <template #prepend>
            <v-icon color="success">mdi-arrow-down</v-icon>
          </template>
          {{CA[average.downlinkNetworkQuality]}}
        </v-chip>
      </template>
      <v-btn color="info" density="compact" variant="text" @click="detection" :loading="loading">{{first ? '开始':'重新'}}检测</v-btn>
    </v-col>
  </v-row>
  <v-row>
    <v-col align="center">
      <v-btn class="bg-mc-bule-lighten-1 ml-2" variant="text"  @click="emit('closeSelf')">
        开始使用
      </v-btn>
    </v-col>
  </v-row>
</template>

<script lang='ts' setup>
import { onMounted, ref, getCurrentInstance, ComponentInternalInstance, computed, watch } from 'vue';
import detect, {testVolumeQuality, stopVolumeQuality} from '@/utils/detect';
import TRTC from '@/services/trtc';
import { storeToRefs } from 'pinia';
import useRoomStore from '@/store/room';
import { URLParams, toAwait, httpRequest } from '@/utils/utils';
import TUIMessage from '@/components/TUI/Message/index';

async function getUserSig() {
  return await httpRequest({
    url: 'api/web/teaching-room/auth.htm',
    params: {
      roomNo: URLParams.roomNo
    },
    host: 'parrot',
  })
}

const { appContext } = getCurrentInstance() as ComponentInternalInstance
const roomStore = useRoomStore();
const { currentMicrophoneId, microphoneList, currentSpeakerId, speakerList } = storeToRefs(roomStore);
let loading = ref(false)
let first = ref(true)
let isTest = ref(false)
let volume = ref(0)
let average = ref({
  uplinkNetworkQuality: 0,
  downlinkNetworkQuality: 0,
  RTT: 0,
})

const emit = defineEmits(['closeSelf']);

const volumeNum = computed(() => {
  return volume.value * 24 / 100;
});

onMounted(async () => {
  average.value = {
    uplinkNetworkQuality: 0,
    downlinkNetworkQuality: 0,
    RTT: 0,
  }
  getDevice()
})

var CA = {0:"\u672A\u77E5",1:"\u6781\u4F73",2:"\u8F83\u597D",3:"\u4E00\u822C",4:"\u5DEE",5:"\u6781\u5DEE",6:"\u65AD\u5F00"}

async function getDevice() {
  if (!currentMicrophoneId.value) {
    let microphoneList = await TRTC.getMicrophoneList()
    let speakerList = await TRTC.getSpeakerList()
    microphoneList && roomStore.setMicrophoneList(microphoneList);
    speakerList && roomStore.setSpeakerList(speakerList);
  }
}
function stopVolume() {
  isTest.value = false
  stopVolumeQuality()
  volume.value = 0
}
async function testVolume() {
  if (!currentMicrophoneId.value) {
    TUIMessage({
      type: 'error',
      message: '未找到麦克风',
      duration: 3000,
      appContext
    })
    return
  }
  const [err, res] = await toAwait(getUserSig())
  if (err) {
    TUIMessage({
      type: 'error',
      message: err.message,
      duration: 5000,
      appContext
    })
    return
  }
  const {
    sdkAppId,
    trtcUserId: userId,
    trtcUserSig: userSig,
  } = res
  isTest.value = true
  const myVolume = testVolumeQuality({
    sdkAppId,
    userId,
    userSig,
    currentMicrophoneId: currentMicrophoneId.value
  })
  const stop = watch(myVolume, (val) => {
    volume.value = val
  })
}
async function detection() {
  if (!currentMicrophoneId.value) {
    TUIMessage({
      type: 'error',
      message: '未找到麦克风',
      duration: 3000,
      appContext
    })
    return
  }
  const [err, res] = await toAwait(getUserSig())
  if (err) {
    TUIMessage({
      type: 'error',
      message: err.message,
      duration: 5000,
      appContext
    })
    return
  }
  loading.value = true
  first.value = false
  const {
    sdkAppId,
    trtcUserId: userId,
    trtcUserSig: userSig,
  } = res
  const resultWithNetwork = await detect({
    sdkAppId,
    userId,
    userSig,
    currentMicrophoneId: currentMicrophoneId.value
  });
  average.value = resultWithNetwork.average
  loading.value = false
}
</script>