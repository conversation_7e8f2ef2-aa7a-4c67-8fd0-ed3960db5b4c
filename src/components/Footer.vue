<template>
  <v-row no-gutters align="center" class="mx-auto" style="max-width: 72%; min-width: 1200px;">
    <v-col>
      <AudioControl />
      <v-btn :prepend-icon="fullscreen ? 'mdi-fullscreen-exit': 'mdi-fullscreen'" stacked variant="text" @click="toggleFullScreen">
        <template #prepend>
          <v-icon class="bg-mc-bule-darken-3 opacity-80 w-auto h-auto pa-2 rounded-pill" size="x-large"></v-icon>
        </template>
        <span v-if="fullscreen">退出全屏</span>
        <span v-else>全屏</span>
      </v-btn>
    </v-col>
    <v-col cols="auto" v-if="roomStore.isMaster">
      <v-btn v-if="roomStore.status === 1" class="bg-mc-bule-lighten-1" size="x-large" variant="text" @click="handleEnter" :width="200">
        开始上课
      </v-btn>
      <v-hover v-else-if="roomStore.status === 2"
        v-slot="{ isHovering, props }"
      >
        <v-btn class="bg-mc-red-darken-1 py-1" size="x-large" variant="text" @click="handleExit" :width="200" v-bind="props">
          <template #prepend>
              <v-icon size="24">
                <img src="../assets/svg/power.svg" />
              </v-icon>
            </template>
          <div style="width: 96px">
            <v-expand-transition mode="default">
              <div v-if="isHovering">
                结束上课
              </div>
              <div v-else>{{ timeStr }}</div>
            </v-expand-transition>
          </div>
        </v-btn>
      </v-hover>
    </v-col>
    <v-col align="end">
      <v-overlay
        v-if="roomStore.isMaster"
        attach="#main-target"
        transition="slide-x-reverse-transition"
        height="100%"
        width="360"
        :scrim="false"
        class="dialog-right-side justify-end"
      >
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" stacked variant="text">
            <template #prepend>
              <v-icon size="48">
                <img src="../assets/svg/folder-open.svg" />
              </v-icon>
            </template>
            <span>学员档案</span>
          </v-btn>
        </template>
        <template v-slot:default="{ isActive }">
          <v-card class="d-flex flex-column bg-mc-bule-darken-7" height="100%" elevation="0" rounded="0">
            <v-toolbar density="compact" class="text-mc-gray-lighten-1 bg-mc-bule-darken-8">
              <div class="text-subtitle-2 pl-4">学员档案</div>
              <v-select
                :items="carTypeItems"
                hide-details
                single-line
                density="compact"
                v-model="carType"
                class="flex-0-0 ml-2"
              >
              </v-select>
              <v-select
                :items="kemuItems"
                hide-details
                single-line
                density="compact"
                v-model="kemu"
                class="flex-0-0 ml-2"
              >
              </v-select>
              <v-spacer></v-spacer>
              <v-btn icon="mdi-close" variant="text" size="small" @click="isActive.value = false"></v-btn>
            </v-toolbar>
            <StudentDetail v-if="kemu" :kemuParams="kemuParams" :carType="carType" :tutorKemu="kemu" @closeSelf="isActive.value = false" />
          </v-card>
        </template>
      </v-overlay>

      <v-btn v-if="roomStore.isMaster" :disabled="roomStore.status === 1 || examQuestionIds.length === 0" stacked variant="text" @click="startExam">
        <template #prepend>
          <v-icon size="48">
            <img src="../assets/svg/text-box-edit.svg" />
          </v-icon>
        </template>
        <span>随堂测验</span>
      </v-btn>

      <v-dialog v-model="examDialogVisible" :max-width="390" height="500" content-class="ma-0" persistent>
        <ExamDialog @closeSelf="closeExamDialog" />
      </v-dialog>

      <v-dialog v-model="examResultDialogVisible" width="400" :max-height="500">
        <ExamResultDialog @closeSelf="closeExamResultDialog" />
      </v-dialog>

      <v-overlay
        v-if="roomStore.isMaster"
        attach="#main-target"
        transition="slide-x-reverse-transition"
        height="100%"
        width="360"
        :scrim="false"
        class="dialog-right-side justify-end">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" stacked class="px-1" variant="text">
            <template #prepend>
              <v-icon size="48">
                <img src="../assets/svg/book-open-variant.svg" />
              </v-icon>
            </template>
            <span>教案</span>
          </v-btn>
        </template>
        
        <template v-slot:default="{ isActive }">
          <v-card class="d-flex flex-column bg-mc-bule-darken-7" height="100%" elevation="0" rounded="0">
            <v-toolbar density="compact" class="text-mc-gray-lighten-1 bg-mc-bule-darken-8">
              <div class="text-subtitle-2 pl-4">教案</div>
              <v-spacer></v-spacer>
              <v-btn icon="mdi-close" variant="text" size="small" @click="isActive.value = false"></v-btn>
            </v-toolbar>
            <TeachPlan @closeSelf="isActive.value = false"></TeachPlan>
          </v-card>
        </template>
      </v-overlay>

      <v-overlay
        v-if="roomStore.isMaster"
        attach="#main-target"
        transition="slide-x-reverse-transition"
        height="100%"
        width="360"
        :scrim="false"
        class="dialog-right-side justify-end">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" stacked class="px-1" variant="text">
            <template #prepend>
              <v-icon size="48">
                <img src="../assets/svg/folder-open.svg" />
              </v-icon>
            </template>
            <span>资料库</span>
          </v-btn>
        </template>
        
        <template v-slot:default="{ isActive }">
          <v-card class="d-flex flex-column bg-mc-bule-darken-7" height="100%" elevation="0" rounded="0">
            <v-toolbar density="compact" class="text-mc-gray-lighten-1 bg-mc-bule-darken-8">
              <div class="text-subtitle-2 pl-4">资料库</div>
              <v-spacer></v-spacer>
              <v-btn icon="mdi-close" variant="text" size="small" @click="isActive.value = false"></v-btn>
            </v-toolbar>
            <PublicMaterial @closeSelf="isActive.value = false"></PublicMaterial>
          </v-card>
        </template>
      </v-overlay>

      <v-overlay
        v-if="roomStore.isMaster || roomStore.isSauListener"
        attach="#main-target"
        transition="slide-x-reverse-transition"
        height="100%"
        width="360"
        :scrim="false"
        class="dialog-right-side justify-end">
        <template v-slot:activator="{ props }">
          <v-btn :disabled="roomStore.status !== 2" v-bind="props" stacked class="px-1" variant="text">
            <template #prepend>
              <v-icon size="48">
                <img src="../assets/svg/account-multiple.svg" />
              </v-icon>
            </template>
            <span>成员<template v-if="roomStore.status === 2">({{ roomStore.userList.length }})</template></span>
          </v-btn>
        </template>
        <template v-slot:default="{ isActive }">
          <v-card class="d-flex flex-column bg-mc-bule-darken-7" height="100%" elevation="0" rounded="0">
            <v-toolbar density="compact" class="text-mc-gray-lighten-1 bg-mc-bule-darken-8">
              <div class="text-subtitle-2 pl-4">成员</div>
              <v-spacer></v-spacer>
              <v-btn icon="mdi-close" variant="text" size="small" @click="isActive.value = false"></v-btn>
            </v-toolbar>
            <UserList></UserList>
          </v-card>
        </template>
      </v-overlay>

      <v-overlay
        attach="#main-target"
        transition="slide-x-reverse-transition"
        height="100%"
        width="360"
        :scrim="false"
        class="dialog-right-side justify-end">
        <template v-slot:activator="{ props }">
          <v-btn :disabled="!chatStore.isTimReady" v-bind="props" stacked class="px-1" variant="text">
            <template #prepend>
              <v-icon size="48">
                <img src="../assets/svg/tooltip-text.svg" />
              </v-icon>
            </template>
            <span>聊天</span>
          </v-btn>
        </template>
        <template v-slot:default="{ isActive }">
          <v-card class="d-flex flex-column bg-mc-bule-darken-7" height="100%" elevation="0" rounded="0">
            <v-toolbar density="compact" class="text-mc-gray-lighten-1 bg-mc-bule-darken-8">
              <div class="text-subtitle-2 pl-4">聊天</div>
              <v-spacer></v-spacer>
              <v-btn icon="mdi-close" variant="text" size="small" @click="isActive.value = false"></v-btn>
            </v-toolbar>
            <MessageList></MessageList>
            <ChatEditor class="bg-transparent"></ChatEditor>
          </v-card>
        </template>
      </v-overlay>
    </v-col>
  </v-row>
</template>

<script lang='ts' setup>
import { ref, watch, getCurrentInstance, computed } from 'vue';
import useBasicStore from '@/store/basic';
import useRoomStore from '@/store/room';
import useChatStore from '@/store/chat';
import AudioControl from '@/components/AudioControl.vue';
import UserList from '@/components/UserList.vue';
import MessageList from '@/components/MessageList';
import ChatEditor from '@/components/ChatEditor';
import StudentDetail from '@/components/StudentDetail.vue';
import TeachPlan from '@/components/TeachPlan.vue';
import PublicMaterial from '@/components/PublicMaterial.vue';
import ExamDialog from '@/components/ExamDialog.vue';
import ExamResultDialog from '@/components/ExamResultDialog.vue';
import { formatTimeStr, URLParams, httpRequest, trackEvent } from '@/utils/utils';
import TUIMessageBox from '@/components/TUI/MessageBox/index';
import useExamStatus from '@/hooks/useExamStatus';

const { appContext } = getCurrentInstance()!
const basicStore = useBasicStore();
const roomStore = useRoomStore();
const chatStore = useChatStore();
const {setActionStatus, getActionStatus, sendStartExam, onStartExam, showExamDialog, onSubmitExam} = useExamStatus()
const emit = defineEmits(['enterRoom', 'exitRoom']);
let startTime = ref(0)
let timeStr = ref('')
let timer: NodeJS.Timeout
let examQuestionIds = ref([])
let examDialogVisible = ref(false)
let examResultDialogVisible = ref(false)
let fullscreen = ref(false)
let carType = ref('')
let carTypeItems = ref([
  {
    title: '小车',
    value: 'car'
  },
  {
    title: '客车',
    value: 'bus'
  },
  {
    title: '货车',
    value: 'truck'
  },
  {
    title: '摩托',
    value: 'moto'
  },
])
let kemu = ref(0)
let kemuItems = ref([
  {
    title: '科一',
    value: 10,
    params: {
      kemu: 1,
      sceneCode: 101
    }
  },
  {
    title: '科四',
    value: 20,
    params: {
      kemu: 4,
      sceneCode: 101
    }
  },
  {
    title: '扣满12分',
    value: 30,
    params: {
      kemu: 1,
      sceneCode: 102
    }
  },
  {
    title: '恢复驾驶证',
    value: 40,
    params: {
      kemu: 1,
      sceneCode: 101
    }
  },
])

const kemuParams = computed(() => {
  const kemuSelected = kemuItems.value.find(item => {
    return item.value === kemu.value
  })
  return kemuSelected?.params as {
    kemu: number,
    sceneCode: number
  }
})

async function getExamQuestionIds() {
  return httpRequest({
    url: 'api/web/course/get-course-examine-questions.htm',
    params: {
      courseId: URLParams.courseId,
    },
    host: 'parrot',
  })
}

async function getStudentBaseInfo() {
  return httpRequest({
    url: 'api/web/student/get-student-base-info.htm',
    params: {
      sno: basicStore.sno,
    },
    host: 'parrot',
  })
}


watch(() => roomStore.status, async (val) => {
  if (val === 2) {
    startTimer()
  } else {
    stopTimer()
  }
})

watch(() => basicStore.sdkAppId, async () => {
  if (roomStore.isMaster) {
    getStudentBaseInfo().then(res => {
      const { carType: _carType, tutorKemu } = res
      carType.value = _carType || 'car'
      kemu.value = tutorKemu || 10
    }).catch(() => {
      carType.value = 'car'
      kemu.value = 10
    })
    const {questionIds} = await getExamQuestionIds()
    if (questionIds?.length) {
      examQuestionIds.value = questionIds
      onSubmitExam(() => {
        setTimeout(() => {
          examResultDialogVisible.value = true
        }, 5000)
        setActionStatus(URLParams.courseId, 'submit')
      })
    }
  } else {
    const result = showExamDialog(URLParams.courseId)
    if (result) {
      examDialogVisible.value = true
    } else {
      onStartExam(() => {
        examDialogVisible.value = true
      })
    }
  }
})

document.addEventListener("fullscreenchange", function() {
  if (document.fullscreenElement) {
    fullscreen.value = true
  } else {
    fullscreen.value = false
  }
});

function closeExamDialog() {
  examDialogVisible.value = false
}
function closeExamResultDialog() {
  examResultDialogVisible.value = false
}

function startTimer() {
  const saveStartTime = Number(localStorage.getItem(`startTime-${URLParams.courseId}`));
  if (saveStartTime) {
    startTime.value = saveStartTime
  } else {
    startTime.value = +new Date()
    localStorage.setItem(`startTime-${URLParams.courseId}`, String(startTime.value));
  }
  timer = setInterval(() => {
    const now = +new Date()
    timeStr.value = formatTimeStr(now - startTime.value, 'HH:mm:ss')
  }, 500)
}

function stopTimer() {
  clearInterval(timer)
}

function handleEnter() {
  emit('enterRoom');

  trackEvent({
    fragmentName1: '下方工具栏',
    actionType: '点击',
    actionName: '开始上课',
  })
}
function handleExit() {
  const status = getActionStatus(URLParams.courseId)
  let msg = status === 'send' ? '学员还未完成随堂测验，确认下课吗？' : '下课后本节课将无法再次进行上课操作，确认下课吗？'
  TUIMessageBox({
    title: '提示',
    message: msg,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    callback: async (result) => {
      if (result) {
        emit('exitRoom');

        trackEvent({
          fragmentName1: '确认下课弹窗',
          actionType: '点击',
          actionName: '确定',
        })
      }
    },
    appContext
  });
}
function startExam() {
  const status = getActionStatus(URLParams.courseId)
  if (status === 'send') {
    TUIMessageBox({
      title: '提示',
      message: '学员正在进行随堂测验，若学员未收到做题弹窗，可点击再次发送',
      confirmButtonText: '再次发送',
      cancelButtonText: '关闭',
      callback: async (result) => {
        if (result) {
          sendStartExam()
          setActionStatus(URLParams.courseId, 'send')
        }
      },
      appContext
    });
  } else if (status === 'submit') {
    examResultDialogVisible.value = true
  } else {
    TUIMessageBox({
      title: '发起随堂测验',
      message: '请确保完成授课内容后再发起随堂测验。发起后学员端将弹出答题弹窗，请务必告知学员你已发起了随堂测验，并敦促学员在下课前完成答题',
      confirmButtonText: '已完成授课并发起测验',
      cancelButtonText: '取消',
      callback: async (result) => {
        if (result) {
          sendStartExam()
          setActionStatus(URLParams.courseId, 'send')
        }
      },
      appContext
    });
  }
}

function toggleFullScreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
}
</script>

<style lang="scss">
.dialog-right-side {
  top: var(--v-layout-top) !important;
  bottom: var(--v-layout-bottom) !important;
}
</style>
