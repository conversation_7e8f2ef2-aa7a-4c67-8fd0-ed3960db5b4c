type HostConfig = {
    host: string
    devHost: string
    testHost: string
    sign: string
    resign: string
}
type IndexableType = {
    [key: string]: string;
};

const isProd = import.meta.env.MODE === 'production'

export let hosts: IndexableType = {}
export let signs: IndexableType = {}
export let resigns: IndexableType = {}

function addHost(key: string, config: HostConfig) {
    let host
    let {sign, resign} = config
    if (isProd) {
        host = config.host
    } else {
        host = config.testHost
    }
    hosts[key] = host
    signs[key] = sign
    resigns[key] = resign
}

addHost('parrot', {
    host: 'https://parrot.kakamobi.cn/',
    devHost: 'https://parrot.dev.mucang.cn/',
    testHost: 'https://parrot.ttt.mucang.cn/',
    sign: '',
    resign: 'debug',
})

addHost('tiku', {
    host: 'https://jk-tiku.kakamobi.cn/',
    devHost: 'https://jk-tiku.dev.mucang.cn/',
    testHost: 'https://jk-tiku.ttt.mucang.cn/',
    sign: '',
    resign: 'debug',
})