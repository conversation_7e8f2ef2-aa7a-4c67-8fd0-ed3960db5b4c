import { MCProtocol, MCBaseStore, MCBaseUtils } from '@simplex/simple-base'
import type {CoreWebOpenParams, UserInfoModel} from '@simplex/simple-mcprotocol/typings/type.d.ts'

import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import { hosts, signs, resigns } from './serverHost'
import { reload, navigateTo } from './jump'
import { LOCATION } from './constant'
import mcLogin from '../libs/login.esm.js'

const ua = window.navigator.userAgent.toLowerCase()
export const isMobile = /android|weboS|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua);
export const isMucang = ua.indexOf('mucang') > -1
export const isWeixin = ua.indexOf('micromessenger') > -1
export const isAndroid = ua.indexOf('android') > -1
export const isTablet =
    /(?:ipad|playbook)/.test(ua) ||
    (isAndroid && !/(?:mobile)/.test(ua)) ||
    (ua.indexOf('android') > -1 && document.documentElement.clientWidth >= 480)
export const isIOS = ua.indexOf('iphone') > -1 || ua.indexOf('ipad') > -1 || ua.indexOf('ipod') > -1
export const isIpad = ua.indexOf('ipad') > -1
export const isMacOs = ua.indexOf('mac os') > -1 || ua.indexOf('macintosh') > -1
export const isHarmony = ua.indexOf('harmony') > -1

export function getURLParams (filter?: string, str?: string): Record<string, any> {
    var params: Record<string, any> = {};
    var search = window.location.search
    var filterRegx = new RegExp(filter || '', 'gi');;

    if (str) {
        search = (new URL(str)).search
    }
    
    search.substring(1).split('&').forEach(item => {
        const [key, value] = item.split('=');

        params[key] = decodeURIComponent(value);

        if (filter && !key.match(filterRegx)) {
            delete params[key];
        }
    })

    return params;
}

let search = getURLParams()
if (!search.kemu) {
    search.kemu = search.kemuStyle
}
if (search.kemu && !isNaN(search.kemu)) {
    search.kemu = 'kemu' + search.kemu
}
search.kemuNum = (search.kemu || '').slice(4)
search._product = search._product || '驾考宝典'
search._product = search._product === '驾考宝典助手' ? '驾考宝典' : search._product

export let URLParams = search
console.log(getURLParams())

export const isMajorApp =
    (URLParams._appName === 'jiakaozhushou' && isIOS) ||
    (URLParams._appName === 'jiakaobaodian' && isAndroid)

export const isXueTang = URLParams._appName === 'jiakaoxuetang' || URLParams._appName === 'jiakaoduoyuyan'

function getIOSVersion() {
    let IOSVersionMatch = ua.match(/cpu iphone os (.*?) like mac os/)
    let IOSVersionRes = IOSVersionMatch?.[1]
    let IOSVersionArr = IOSVersionRes?.split('_')
    if (IOSVersionArr) {
        IOSVersionArr.length = Math.min(2, IOSVersionArr.length)
        return +IOSVersionArr.join('.')
    }
    return null
}

function getAndroidVersion() {
    let androidVersionMatch = ua.match(/android\s([0-9.]*)/)
    let androidVersionRes = androidVersionMatch?.[1]
    let androidVersionArr = androidVersionRes?.split('.')
    if (androidVersionArr) {
        androidVersionArr.length = Math.min(2, androidVersionArr.length)
        return +androidVersionArr.join('.')
    }
    return null
}
export const IOSVersion = getIOSVersion()
export const androidVersion = getAndroidVersion()
let fontSize = parseFloat(document.documentElement.style.fontSize)

export function compareVersionWithBizCode(targetVersion: string) {
    return MCBaseUtils.compareVersion(search.bizCode, targetVersion)
}

export function goLogin(config = {refresh: false}) {
    return new Promise(resolve => {
        let _login = async () => {
            const resData = await login(config)
            if (!config.refresh) {
                // TODO 广播登录成功
            }
            resolve(resData)
        }

        _login()
    })
}
function login(config = {refresh: false}) {
    return new Promise(resolve => {
        let done = false
        const callback = () => {
            if (done) return
            done = true
            if (config.refresh) {
                reload()
            } else {
                resolve(true)
            }
        }
        window.loginSuccess = () => {
            callback()
        }
        if (isMucang) {
            MCProtocol.Core.User.login({
                from: 'jiakaobaodian',
                skipAuthRealName: true,
                pageType: 'quicklogin',
                callback: (data) => {
                    if (data && data.success) {
                        callback()
                        console.log('login success')
                    }
                },
            })
        } else {
            mcLogin({
                loginOptions: {
                    _appName: 'jiakaoshouke'
                },
                callback: () => {
                    console.log('login callback')
                    callback()

                    statInit()
                },
            })
        }
    })
}

export function webOpen(config: CoreWebOpenParams & {url: string}) {
    let { url } = config
    if (isMucang) {
        MCProtocol.Core.Web.open(config)
    } else {
        navigateTo(url)
    }
}

function getDefaultKey() {
    const defaultKeys = [
        'carStyle',
        'kemuStyle',
        'score12',
        'sceneCode',
        'patternCode',
        'fromPage',
        'fragmentName1',
        'fromPageCode',
        'fromItemCode',
        'questionId',
        'subject',
        'courseId',
        'bizVersion',
        'fromPathCode',
    ]

    return defaultKeys.join(',')
}

/** 新开一个vip的webview */
export function openVipWebView(config: CoreWebOpenParams & {url: string}) {
    if (!isMucang) {
        window.open(config.url)
        return
    }

    if (isIOS) {
        webOpen({
            url:
                'http://jiakao.nav.mucang.cn/buyWebView?url=' +
                encodeURIComponent(config.url) +
                '&keys=' +
                getDefaultKey(),
        })
    } else {
        webOpen({
            url: 'http://jiakao.nav.mucang.cn/vip/new-vip?page=' + encodeURIComponent(config.url),
        })
    }
}
export function openNewVip(config: CoreWebOpenParams & {url: string}) {
    if (!isMucang) {
        window.open(config.url)
        return
    }

    webOpen({
        url: 'http://jiakao.nav.mucang.cn/vip/new-vip?page=' + encodeURIComponent(config.url),
    })
}

export function toast(message: string, time?: number, callback?: Function, options = {foz: '', frozen: false}) {
    let { foz, frozen } = options
    if (!message) {
        return
    }

    if (document.getElementById('myToast')) {
        document.body.removeChild(document.getElementById('myToast')!)
    }

    var div = document.createElement('div')
    var mask: HTMLElement
    div.innerText = message

    div.setAttribute('id', 'myToast')

    div.style.position = 'fixed'
    div.style.left = '50%'
    div.style.top = '50%'
    div.style.transform = 'translate(-50%, -50%)'
    div.style.webkitTransform = 'translate(-50%, -50%)'
    div.style.background = 'rgba(0, 0, 0, 0.7)'
    div.style.zIndex = '9999'
    div.style.padding = '0.15rem'
    div.style.borderRadius = '8px'
    div.style.textAlign = 'center'
    div.style.color = '#ffffff'
    div.style.maxWidth = '85%'
    div.style.fontSize = foz || '0.28rem'
    div.style.lineHeight = '1.5'

    time = time || 2000
    document.body.appendChild(div)
    if (frozen) {
        mask = document.createElement('div')
        mask.style.position = 'fixed'
        mask.style.left = '0'
        mask.style.top = '0'
        mask.style.width = '100%'
        mask.style.height = '100%'
        mask.style.zIndex = '9998'

        document.body.appendChild(mask)
    }
    setTimeout(function () {
        div.remove()
        mask && mask.remove()
        callback && callback()
    }, time)
}

export function convertParamsStr(data: {
    [key: string]: any
}) {
    let url = ''
    for (var k in data) {
        let value = data[k] !== undefined ? data[k] : ''
        url += `&${k}=${encodeURIComponent(value)}`
        // url += `&${k}=${value}`
    }
    return url ? url.substring(1) : ''
}

export function getUrl(url: string, data: {
    [key: string]: any
}) {
    var params = getURLParams('', url)
    var hash = url.match(/#/) ? url.split('#')[1] : ''
    params = Object.assign(params, data)
    url = url.substring(0, url.indexOf('?') > 0 ? url.indexOf('?') : url.length)
    url = url.substring(0, url.indexOf('#') > 0 ? url.indexOf('#') : url.length)
    url = (url += '?' + convertParamsStr(params))
    // 把丢失的#hash带上
    if (hash) {
        url = (url += '#' + hash)
    }
    return url
}

function paramsExtra(params: {
    [key: string]: any
}) {
    return Object.assign({
        pageName: '授课工具',
        courseId: search.courseId,
    }, params)
}

export function trackEvent(params: {
    [key: string]: any
}) {
    stat.trackEvent(paramsExtra(params))
}

export async function statInit() {
    let authToken = ''
    if (!isMucang) {
        authToken = JSON.parse(localStorage.getItem('mucang_userInfo') || '{}')?.authToken || ''
    }
    stat.init({
        appName: 'jiakaoshouke',
        productCategory: 'jiakaoshouke',
        product: '驾考授课工具',
        joinStr: '_',
        authToken,
    })
}

export function getUUID() {
    var S4 = function () {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
    }

    return S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4()
}

export function isVIVO() {
    var manufacturer = (URLParams._manufacturer && URLParams._manufacturer.toLowerCase()) || ''

    if (manufacturer.indexOf('vivo') > -1) {
        return true
    }

    return false
}

function getUserInfo():Promise<UserInfoModel> {
    return new Promise(resolve => {
        if (isMucang) {
            MCProtocol.Core.User.get(data => {
                const {success} = data
                let obj = {} as UserInfoModel
                if (success?.toString() === 'true') {
                    obj = data.data!
                }
                resolve(obj)
            })
        } else {
            let userinfo = JSON.parse(localStorage.getItem(LOCATION.USERINFO) || '{}')
            resolve(userinfo)
        }
    })
}
export function getAuthToken() {
    return new Promise(resolve => {
        if (search.authToken) {
            resolve(search.authToken)
        } else {
            getUserInfo().then(data => {
                resolve(data.authToken)
            })
        }
    })
}
export function getMucangId() {
    return new Promise(resolve => {
        getUserInfo().then(data => {
            resolve(data.mucangId)
        })
    })
}

export const getRandomR = MCBaseUtils.getRandomR

type RequestConfig = {
    url: string,
    params: {
        [key: string]: any
    },
    method?: 'post' | 'get',
    host: string,
    showErrorInfo?: boolean,
    jsonForm?: boolean,
    unloginDoNotReload?: boolean
}
export async function httpRequest(config: RequestConfig) {
    let params = config.params || {}
    let method = config.method || 'get'
    let host = config.host
    let hostName = hosts[host]
    let sign = signs[host]
    let resign = resigns[host]

    const authToken = await getAuthToken()
    if (authToken && !params.authToken) {
        params.authToken = authToken
    }

    function genAppUser() {
        let _appUser = localStorage.getItem('_appUser')
        if (!_appUser) {
            _appUser = getUUID()
            localStorage.setItem('_appUser', _appUser)
        }
        return _appUser
    }

    return new Promise((resolve, reject) => {
        let showErrorInfo = config.showErrorInfo
        delete config.showErrorInfo

        let failed = (data: {statusText?: string, message?: string}) => {
            let msg = data?.statusText || data?.message as string
            if (showErrorInfo) {
                toast(msg)
            }
            reject(data)
        }

        if (!isMucang) {
            params.resign = resign
            params._r = getRandomR(1)
            params._appUser = search._appUser || genAppUser()
        }

        let item: {id: string}
        let resData = {}

        MCBaseStore.extend({
            url: hostName + config.url,
            sign: sign,
            method: method,
            type: 'online',
            ajaxOptions: config.jsonForm ? {
                headers: {
                    'Content-Type': 'application/json',
                },
                contentType: false,
                processData: false,
            } : undefined,
        })
            .create({ errorToast: false })
            .request(config.jsonForm ? JSON.stringify(params) : params).then((res: {
                [key: string]: any
            }, data: any) => {
                resolve(dataWebpHelper(res))

                if (isMucang && window?.vConsole) {
                    window.vConsole?.network?.update(item.id, {
                        status: data.statusCode,
                        // readyState: XMLHttpRequest['readyState'],
                        // responseType: XMLHttpRequest['responseType'],
                        // requestType: 'xhr',
                        response: data.data,
                        endTime: +new Date(),
                        ...resData,
                    })
                }
            }).fail((_errorCode: any, error: {text: string, statusCode: number}) => {
                let errorData: {
                    errorCode?: number,
                    statusText?: string,
                    message?: string,
                } = {}
                try {
                    errorData = JSON.parse(error.text)
                } catch (error) { }
                if (errorData?.errorCode === 403) {
                    localStorage.removeItem(LOCATION.AUTHTOKEN)
                    localStorage.removeItem(LOCATION.USERINFO)
                    if (!config.unloginDoNotReload) {
                        // window.location.reload()
                    }
                }
                sendWarning(2, {
                    url: hostName + config.url,
                    params,
                    content: errorData
                })
                failed(errorData)

                if (isMucang && window?.vConsole) {
                    window.vConsole?.network?.update(item.id, {
                        status: error.statusCode,
                        // readyState: XMLHttpRequest['readyState'],
                        // responseType: XMLHttpRequest['responseType'],
                        // requestType: 'xhr',
                        response: errorData,
                        endTime: +new Date(),
                        ...resData,
                    })
                }
            })

        if (isMucang && window?.vConsole) {
            item = window.vConsole?.network?.add({
                method: method,
                url: hostName + config.url,
                startTime: +new Date(),
            })
            resData = {
                [method.toLowerCase() + 'Data']: params,
            }
        }
    })
}
interface SimpleKeyValueObject {
    [key: string]: any
} 
type UType = [] | SimpleKeyValueObject | string
export function getType(obj: UType) {
    let str = Object.prototype.toString.call(obj) // 检测基本类型值，引用类型值的类型
    let map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'unfefined',
        '[object Null]': 'null',
        '[object Object]': 'object',
    }
    return map[str as keyof typeof map]
}

export const formatDate = MCBaseUtils.format.date

const timeUnits = [
	['Y', 1000 * 60 * 60 * 24 * 365],
	['M', 1000 * 60 * 60 * 24 * 30],
	['D', 1000 * 60 * 60 * 24],
	['H', 1000 * 60 * 60],
	['m', 1000 * 60],
	['s', 1000],
	['S', 1]
];
function padStart0(num: string, len: number) {
	if (num.length >= len) {
		return num;
	} else {
		return padStart0('0' + num, len);
	}
}
export function formatTimeStr(duration: number, format: string) {
	let leftDuration = duration;
	const escapeRegex = /\[[^\]]*]/g;
	const keepList = (format.match(escapeRegex) || []).map(function (str) {
		return str.slice(1, -1);
	});
	const templateText = format.replace(escapeRegex, '[]');
	const replacedText = timeUnits.reduce(function (current, _ref) {
		const name = _ref[0] as string;
		const unit = _ref[1] as number;
		if (current.indexOf(name) !== -1) {
			const value = Math.floor(leftDuration / unit);
			leftDuration -= value * unit;
			return current.replace(new RegExp(`${name}+`, 'g'), function (match) {
				const len = match.length;
				return padStart0(String(value), len);
			});
		}
		return current;
	}, templateText);
	let index = 0;
	return replacedText.replace(escapeRegex, function () {
		const match = keepList[index];
		index += 1;
		return match;
	});
}

export function dataWebpHelper(data: {
    [key: string]: any
}) {
    if (isIOS && IOSVersion! < 14) {
        return webpSupportRecursion(data)
    } else {
        return data
    }
}
export function webpSupportRecursion(data: UType): UType {
    let dataType = getType(data)
    let result: UType
    if (dataType === 'array') {
        let t = data as []
        result = t.map(item => {
            return webpSupportRecursion(item)
        })
    } else if (dataType === 'object') {
        result = {}
        let t = data as SimpleKeyValueObject
        for (let key in t) {
            result[key] = webpSupportRecursion(t[key])
        }
    } else {
        result = webpSupport(data as string)
    }
    return result
}
export function webpSupport(src: string) {
    let type = getType(src)
    if (type === 'string') {
        if ((src as string).match(/\.webp$/) && IOSVersion! >= 11 && IOSVersion! < 14 && isMucang) {
            src = (src as string).replace('https:', 'mc-ios-core-webp-image:')
        } else if ((src as string).match(/\.(.+)!\1$/)) {
            src = (src as string).replace(/(!.*)$/gi, '')
        }
    }
    return src
}

// let url = 'http://jiakao.nav.mucang.cn/vip/new-vip?from=201&page=%2fjkbd-vip%2findex%2findex.html%3FactivePanel%3Dpanel2&pagename=allKemu'
// let query = 'page'
// let params = {fromItemCode: 3102}
// let newUrl = addParamsForUrlQueryFun(url, query, params)
// console.log(newUrl) > 'http://jiakao.nav.mucang.cn/vip/new-vip?from=201&page=%2Fjkbd-vip%2Findex%2Findex.html%3FactivePanel%3Dpanel2%26fromItemCode%3D3102&pagename=allKemu'
export function addParamsForUrlQueryFun(url: string, query: string, params: {
    [key: string]: any
}) {
    let urlQuery = getURLParams('', url)[query]

    if (urlQuery) {
        urlQuery = getUrl(urlQuery, params)

        url = getUrl(url, {
            [query]: urlQuery,
        })
    }
    return url
}
function fallbackCopyTextToClipboard(text: string) {
    const textArea = document.createElement('textarea');
    let successful = false
    textArea.value = text;

    // 防止在移动设备上弹出键盘
    textArea.setAttribute('readonly', '');
    textArea.style.position = 'absolute';
    textArea.style.left = '-9999px';

    document.body.appendChild(textArea);
    textArea.select();
    try {
        successful = document.execCommand('copy');
    } catch (err) {
        console.error('无法复制', err);
    }

    document.body.removeChild(textArea);
    return successful
}
export async function copyText(text: string) {
    if (!navigator.clipboard) {
        return fallbackCopyTextToClipboard(text);
    }
    const [err] = await toAwait(navigator.clipboard.writeText(text))
    return !err
}

export function addClass(elem: HTMLElement, str: string) {
    let classNames = elem.className?.split(' ')
    if (classNames.indexOf(str) === -1) {
        classNames.push(str)
        elem.className = classNames.join(' ')
    }
}

export function removeClass(elem: HTMLElement, str: string) {
    let classNames = elem.className?.split(' ')
    let index = classNames.indexOf(str)
    if (index !== -1) {
        classNames.splice(index, 1)
        elem.className = classNames.join(' ')
    }
}

export function convertPixelFromUI(value: number) {
    return ((value / 2) * fontSize) / (100 / 2)
}

export function errorToObject(error: Error) {
    const { name, message, stack } = error;
    return {
        name,
        message,
        stack: stack?.split('\n').map(line => line.trim())
    };
}

export function sendWarning(pathType: number, playload: {
    [key: string]: any
}) {
    let pageName = '公共页'
    let fragmentName1 = 'h5页面'
    let actionType = '触发'
    let actionName = '异常上报'

    // 公共页_h5页面_触发异常上报
    stat.trackEvent({
        pageName,
        fragmentName1,
        actionType,
        actionName,
        pageUrl: location.href,
        _pageName: '授课工具',
        pathType,
        playload,
        eventId: 'debug',
    })
}

export function loadScript(url: string) {
    let script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = url
    script.async = false
    document.head.appendChild(script)
    return new Promise(resolve => {
        script.onload = function () {
            resolve(true)
        }
    })
}

export function loadStyle(url: string) {
    let style = document.createElement('link')
    style.rel = 'stylesheet'
    style.href = url
    document.head.appendChild(style)
}

export const wakeup = function (url: string) {
    if (!url) {
        url = 'http://jiakao.nav.mucang.cn/tab?name=jiakao'
    }
    try {
        // TODO oppo
        window.location.href = `mucang-jiakaobaodian://gateway?navUrl=${encodeURIComponent(url)}`
        throw new Error()
    } catch (error) {
        setTimeout(function () {
            if (!document.hidden) {
                window.location.href =
                    'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-jiaoliantuiguang/down1.html?channelCode=zhibo'
            }
        }, 500)
    }
}

/** 获取系统信息 */
export function getSystemInfo() {
    return new Promise(resolve => {
        if (isMucang) {
            MCProtocol.Core.System.info(baseParams => {
                if (baseParams.data) {
                    if (typeof baseParams.data === 'string') {
                        baseParams = JSON.parse(baseParams.data)
                    } else {
                        baseParams = baseParams.data
                    }
                } else if (typeof baseParams === 'string') {
                    baseParams = JSON.parse(baseParams)
                }
                resolve(baseParams)
            })
        } else {
            resolve(search)
        }
    })
}

export function toChineseNum(num: number) {
    let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    let unit = ['', '十', '百', '千', '万']
    num = Math.floor(num)
    let getWan = (temp: string | number) => {
        let strArr = temp
            .toString()
            .split('')
            .reverse()
            .map(Number)
        let newNum = ''
        let newArr: string[] = []
        strArr.forEach((item, index) => {
            newArr.unshift(item === 0 ? changeNum[item] : changeNum[item] + unit[index])
        })
        let numArr: number[] = []
        newArr.forEach((m, n) => {
            if (m !== '零') numArr.push(n)
        })
        if (newArr.length > 1) {
            newArr.forEach((m, n) => {
                if (newArr[newArr.length - 1] === '零') {
                    if (n <= numArr[numArr.length - 1]) {
                        newNum += m
                    }
                } else {
                    newNum += m
                }
            })
        } else {
            newNum = newArr[0]
        }
        return newNum
    }
    let overWan = Math.floor(num / 10000)
    let noWan: string | number = num % 10000
    if (noWan.toString().length < 4) {
        noWan = '0' + noWan
    }
    return overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num)
}

export function toAwait(promise: Promise<any>) {
    return promise
        .then((data: any) => {
            return [, data]
        })
        .catch((err: Error) => [err])
}

export async function getHost() {
    const isProd = import.meta.env.MODE === 'production'
    const host = isProd ? 'https://laofuzi.kakamobi.com/' : 'https://laofuzi.ttt.kakamobi.com/'
    // const host = 'https://172-20-104-42-5558.local.mucang.cn/'
    return host
}