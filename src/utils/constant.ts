export const LOCATION = {
    AUTHTOKEN: 'mucang_authToken',
    USERINFO: 'mucang_userInfo',
}

export const SAU_LISTENER = 'sau_trtc_212157297'

export enum TeachType {
    Wrong = 1,
    Special = 2,
    Sprint = 3,
    Exam = 4,
    Knowledge = 5
}
export enum WrongListType {
    // 知识点
    Knowledge = 3,
    // 知识点巩固
    Other = 4,
    // 错题本
    WrongQuestions = 5,
    // 近十场考试
    LastExamWrongQuestions = 6,
    // 模拟考试记录
    Exam = 7,
    // 专项 所有试题
    AllQuestion = 9,

    // 专项练习
    SpecialPractice = 11,

    // 随堂测验 结果
    CourseExamResult = 21,
}


// #/263/main/wrongList?wrongListType=3&contentId=82&specialId=177&name=%E9%81%93%E8%B7%AF%E4%BA%A4%E9%80%9A%E6%A0%87%E7%BA%BF&id=593
export interface WrongListKnowledge {
    wrongListType: WrongListType.Knowledge
    contentId: string
    specialId: string
    name: string
    id: number
}

// 所有错题
// #/1037/main/wrongList?wrongListType=3&contentId=2119&name=%E6%89%80%E6%9C%89%E9%94%99%E9%A2%98
export interface WrongListKnowledge2 {
    wrongListType: WrongListType.Knowledge
    contentId: number
    name: string
}

// #/263/main/wrongList?wrongListType=4&contentId=84
export interface WrongListOther {
    wrongListType: WrongListType.Other
    contentId: number
}

// #/799/main/wrongList?wrongListType=5&categoryId=177&name=%E6%A0%87%E5%BF%97%E9%A2%98&tutorKemu=10&sno=10007
export interface WrongListWrongQuestions {
    wrongListType: WrongListType.WrongQuestions | WrongListType.LastExamWrongQuestions
    categoryId: number
    name: string
    carType: string
    tutorKemu: number
    sno: string
}

// #/799/main/wrongList?wrongListType=7&uniqueId=48b602626758c7a6f274902bfc5aea3e&name=%E8%80%83%E8%AF%95%E9%94%99%E9%A2%98&tutorKemu=10&sno=10007
export interface WrongListExam {
    wrongListType: WrongListType.Exam
    uniqueId: string
    name: string
    carType: string
    tutorKemu: number
    sno: string
}

// #/263/main/wrongList?wrongListType=9&contentId=82&specialId=175&name=%E9%A9%BE%E9%A9%B6%E8%AF%81%E7%9B%B8%E5%85%B3
export interface WrongListAllQuestion {
    wrongListType: WrongListType.AllQuestion
    contentId: string
    specialId: string
    name: string
}

// #/799/main/wrongList?wrongListType=11&specialId=41&name=%E6%96%87%E6%98%8E%E9%A9%BE%E9%A9%B6%E9%A2%98&specialSource=1
export interface WrongListSpecialPractice {
    wrongListType: WrongListType.SpecialPractice
    specialId: number
    name: string
    specialSource: number
}

// #/812/main/wrongList?wrongListType=21&examineEncodeId=0539823a27d98c46fb4574ce916f19d3
export interface WrongListCourseExamResult {
    wrongListType: WrongListType.CourseExamResult
    examineEncodeId: string
}

export enum CoursewareType {
    // 专项
    Special = 1,
    // 考前冲刺
    Exam = 2,
}