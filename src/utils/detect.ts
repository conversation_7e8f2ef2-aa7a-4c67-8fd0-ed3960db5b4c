import TRTC from 'trtc-sdk-v5';
import { ref } from 'vue';
type NETWORK_QUALITY = {downlinkNetworkQuality: number, uplinkNetworkQuality: number, uplinkRTT: number}

let testVolumeTRTC: TRTC;
export function testVolumeQuality(options: {sdkAppId: number, userId: string, userSig: string, currentMicrophoneId: string}) {
  const {sdkAppId, userId, userSig, currentMicrophoneId} = options
  let myVolume = ref(0)
  testVolumeTRTC = TRTC.create();
  testVolumeTRTC.enterRoom({
    roomId: 8080,
    sdkAppId,
    userId,
    userSig,
  })
  
  testVolumeTRTC.on(TRTC.EVENT.AUDIO_VOLUME, (event: any) => {
    event.result.forEach(({ userId, volume }) => {
      const isMe = userId === '';
        if (isMe) {
          console.log(`my volume: ${volume}`);
          myVolume.value = volume
        }
    })
  });
  testVolumeTRTC.enableAudioVolumeEvaluation(500);
  testVolumeTRTC.startLocalAudio({
    option: {
      microphoneId: currentMicrophoneId,
    },
  });
  return myVolume
}
export async function stopVolumeQuality() {
  testVolumeTRTC.exitRoom()
  testVolumeTRTC.destroy()
}
export default async function(options: {sdkAppId: number, userId: string, userSig: string, currentMicrophoneId: string}): Promise<{
  average: {
    uplinkNetworkQuality: number,
    downlinkNetworkQuality: number,
    RTT: number,
  }
}> {
  const {sdkAppId, userId, userSig, currentMicrophoneId} = options
  let uplinkTRTC: TRTC;
  let downlinkTRTC: TRTC;
  let testResult = {
    // 记录上行网络质量数据
    uplinkNetworkQualities: [] as number[],
    // 记录下行网络质量数据
    downlinkNetworkQualities: [] as number[],
    RTTQualities: [] as number[],
    average: {
      uplinkNetworkQuality: 0,
      downlinkNetworkQuality: 0,
      RTT: 0,
    }
  }
  async function testUplinkNetworkQuality() {
    uplinkTRTC = TRTC.create();
    uplinkTRTC.enterRoom({
      roomId: 8080,
      sdkAppId,
      userId,
      userSig,
    })
    uplinkTRTC.on(TRTC.EVENT.NETWORK_QUALITY, (event: NETWORK_QUALITY) => {
      const { uplinkNetworkQuality, uplinkRTT } = event;
      testResult.uplinkNetworkQualities.push(uplinkNetworkQuality);
      testResult.RTTQualities.push(uplinkRTT);
    });
    uplinkTRTC.startLocalAudio({
      option: {
        microphoneId: currentMicrophoneId,
      },
    });
  }
  async function testDownlinkNetworkQuality() {
    downlinkTRTC = TRTC.create();
    downlinkTRTC.enterRoom({
      roomId: 8080,
      sdkAppId,
      userId,
      userSig,
      autoReceiveVideo: true
    });
    downlinkTRTC.on(TRTC.EVENT.NETWORK_QUALITY, event => {
      const { downlinkNetworkQuality } = event;
      testResult.downlinkNetworkQualities.push(downlinkNetworkQuality);
    })
  }
  testUplinkNetworkQuality();
  testDownlinkNetworkQuality();

  return new Promise((resolve) => {
    setTimeout(() => {
      // 计算上行平均网络质量
      const validUplinkNetworkQualitiesList = testResult.uplinkNetworkQualities.filter(value => value >= 1 && value <= 5);
      if (validUplinkNetworkQualitiesList.length > 0) {
        testResult.average.uplinkNetworkQuality = Math.ceil(
          validUplinkNetworkQualitiesList.reduce((value, current) => value + current, 0) / validUplinkNetworkQualitiesList.length
        );
      }
      const validDownlinkNetworkQualitiesList = testResult.uplinkNetworkQualities.filter(value => value >= 1 && value <= 5);
      if (validDownlinkNetworkQualitiesList.length > 0) {
        // 计算下行平均网络质量
        testResult.average.downlinkNetworkQuality = Math.ceil(
          validDownlinkNetworkQualitiesList.reduce((value, current) => value + current, 0) / validDownlinkNetworkQualitiesList.length
        );
      }
      const validRTTQualitiesList = testResult.RTTQualities;
      if (validRTTQualitiesList.length > 0) {
        // 计算下行平均网络质量
        testResult.average.RTT = Math.ceil(
          validRTTQualitiesList.reduce((value, current) => value + current, 0) / validRTTQualitiesList.length
        );
      }
      // 检测结束，清理相关状态。
      uplinkTRTC.exitRoom();
      downlinkTRTC.exitRoom();
      uplinkTRTC.destroy();
      downlinkTRTC.destroy();
      resolve(testResult);
    }, 15 * 1000);
  })
}