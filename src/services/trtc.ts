import TRTC from 'trtc-sdk-v5';
import useBasicStore from '@/store/basic';
import useChatStore from '@/store/chat';
import useRoomStore from '@/store/room';
import TIM, { timInstance } from '@/services/tim';
import {USERROLE} from '@/types/type';
import {SAU_LISTENER} from '@/utils/constant'

let trtc: TRTC

export function getTRTC() {
  if(!trtc) {
    trtc = TRTC.create();
  }
  return trtc
}

export function getUserRole(userId: string): USERROLE {
  if (userId.match(/^l_/)) {
    return USERROLE.TEACHER
  } else if (userId.match(/^sau_/)) {
    return USERROLE.SUPERVISOR
  }
  return USERROLE.STUDENT
}

async function getUserProfile(userList: string[]) {
  const chatStore = useChatStore();
  const basicStore = useBasicStore();

  if (!chatStore.isSdkReady) {
    await new Promise((resolve) => {
      timInstance.on(TIM.EVENT.SDK_READY, () => {
        resolve(true)
      });
    })
  }
  // let promise = timInstance.getGroupMemberList({
  //   groupID: basicStore.roomId,
  // });
  // promise.then(function(imResponse) {
  //   console.log(imResponse.data.memberList);
  // }).catch(function(imError) {
  //   console.warn('getGroupMemberList error:', imError);
  // });
  return await timInstance.getGroupMemberProfile({
    userIDList: userList,
    groupID: basicStore.roomId,
  });
}

function installEventHandlers() {
  trtcInstance.enableAudioVolumeEvaluation(1000);
  trtcInstance.on(TRTC.EVENT.ERROR, handleError);
  trtcInstance.on(TRTC.EVENT.KICKED_OUT, handleKickedOut);
  trtcInstance.on(TRTC.EVENT.REMOTE_USER_ENTER, handleRemoteUserEnter);
  trtcInstance.on(TRTC.EVENT.REMOTE_USER_EXIT, handleRemoteUserExit);
  trtcInstance.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, handleRemoteAudioAvailable);
  trtcInstance.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, handleRemoteAudioUnavailable);
  trtcInstance.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
  trtcInstance.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);
  trtcInstance.on(TRTC.EVENT.AUDIO_VOLUME, handleAudioVolumeChange);
}

function uninstallEventHandlers() {
  trtcInstance.enableAudioVolumeEvaluation(-1);
  trtcInstance.off(TRTC.EVENT.ERROR, handleError);
  trtcInstance.off(TRTC.EVENT.KICKED_OUT, handleKickedOut);
  trtcInstance.off(TRTC.EVENT.REMOTE_USER_ENTER, handleRemoteUserEnter);
  trtcInstance.off(TRTC.EVENT.REMOTE_USER_EXIT, handleRemoteUserExit);
  trtcInstance.off(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, handleRemoteAudioAvailable);
  trtcInstance.off(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, handleRemoteAudioUnavailable);
  trtcInstance.off(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
  trtcInstance.off(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);
  trtcInstance.off(TRTC.EVENT.AUDIO_VOLUME, handleAudioVolumeChange);
}

function handleError() {
  // ElMessage({ message: `Local error: ${error.message}`, type: 'error' });
}

function handleKickedOut() {
  // ElMessage({ message: `User has been kicked out for ${event.reason}`, type: 'warning' });
}

async function handleRemoteUserEnter(event: any) {
  const roomStore = useRoomStore();
  const chatStore = useChatStore();
  console.log('handleRemoteUserEnter', event)
  const { userId } = event;
  const userRole = getUserRole(userId)
  roomStore.addRemoteUser({userId, userRole})
  const imResponse = await getUserProfile([userId])
  const [{nick, avatar, muteUntil}] = imResponse.data?.memberList;
  const isMessageDisabled = muteUntil > +new Date() / 1000
  roomStore.updateUserInfo(userId, {userName: nick, avatarUrl: avatar, isMessageDisabled: isMessageDisabled})

  if (!userId.match('wbpush_') && userId !== SAU_LISTENER) {
    chatStore.updateMessageList({
      ID: Math.random().toString(),
      type: 'TRTCUserEnterMsg',
      payload: {
        text: '',
      },
      avatar: avatar,
      nick: nick || userId,
      from: userId,
      flow: 'in',
      sequence: Math.random(),
    });
  }
}

function handleRemoteUserExit(event: any) {
  const roomStore = useRoomStore();
  const { userId } = event;
  roomStore.removeRemoteUser(userId)
}

function handleRemoteAudioAvailable(event: any) {
  const roomStore = useRoomStore();
  console.log('handleRemoteAudioAvailable', event)
  const {userId} = event
  roomStore.updateUserAudioState(userId, true)
}

function handleRemoteAudioUnavailable(event: any) {
  const roomStore = useRoomStore();
  console.log('handleRemoteAudioUnavailable', event)
  const {userId} = event
  roomStore.updateUserAudioState(userId, false)
}

function handleRemoteVideoAvailable(event: any) {
  const roomStore = useRoomStore();
  console.log('handleRemoteVideoAvailable', event)
  const {userId, streamType} = event
  roomStore.updateUserVideoState(userId, streamType, true)
}

function handleRemoteVideoUnavailable(event: any) {
  const roomStore = useRoomStore();
  console.log('handleRemoteVideoUnavailable', event)
  const {userId, streamType} = event
  roomStore.updateUserVideoState(userId, streamType, false)
}

function handleAudioVolumeChange(event: any) {
  const roomStore = useRoomStore();
  roomStore.setAudioVolume(event.result)
}


export default TRTC

export const trtcInstance = getTRTC()

export async function enterRoom() {
  const basicStore = useBasicStore();
  const roomStore = useRoomStore();

  if (!roomStore.isTrtcReady) {
    await trtcInstance.enterRoom({
      strRoomId: basicStore.roomNo,
      sdkAppId: basicStore.sdkAppId,
      userId: basicStore.userId,
      userSig: basicStore.userSig,
      // autoReceiveAudio: false,
    });
    roomStore.setTrtcReady(true)
  }

  installEventHandlers();
}

export async function exitRoom() {
  await trtcInstance.exitRoom();

  uninstallEventHandlers();
  trtcInstance.destroy();
}