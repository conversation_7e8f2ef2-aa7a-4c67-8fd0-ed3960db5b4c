import { loadScript } from '@/utils/utils'
import useBoardStore from '@/store/board';

declare global {
  interface Window {
    TEduBoard: any
  }
}

export type BoardInitParams = {
  id: string;
  sdkAppId: any;
  userId: any;
  userSig: any;
  classId: any;
  config: {
    boardContentFitMode: any;
    h5PPTDownGradeTimeoutTimes: number;
  };
  styleConfig: {
    brushThin: number;
    selectBoxColor: string;
    selectAnchorColor: string;
    scrollbarThumbColor?: string;
    scrollbarTrackColor?: string;
  };
  authConfig: {
    dataSyncEnable: boolean
  }
}


export let TEduBoard: Window['TEduBoard']

export let boardInstance: Window['TEduBoard']

export function createBoard(initParams: BoardInitParams) {
  if(!boardInstance) {
    boardInstance = new TEduBoard(initParams);
    boardInstance.setLogLevel(1)
  }
  return boardInstance
}

export function getSdk() {
  Promise.all([
    loadScript(
      'https://res.qcloudtiw.com/board/third/axios/axios.min.js'
    ), loadScript(
      'https://res.qcloudtiw.com/board/third/ci/sdk-v0.2.1.js'
    ), loadScript(
      'https://res.qcloudtiw.com/board/third/cos/5.1.0/cos.min.js'
    ), loadScript(
      'https://res.qcloudtiw.com/board/2.9.6/TEduBoard.min.js'
    )
  ]).then(() => {
    TEduBoard = window.TEduBoard
    const boardStore = useBoardStore()
    boardStore.setSdkReady(true)
  })
}