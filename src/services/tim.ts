import TIM, { ChatSD<PERSON> } from 'tim-js-sdk';
import {toAwait} from '@/utils/utils'
import useBasicStore from '@/store/basic';
import useRoomtore from '@/store/room';
import useChatStore from '@/store/chat';

export async function initGroup(options: {roomId: string, userId: string, userSig: string, userName: string, avatar: string, isMaster: boolean}) {
  const {roomId, userId, userSig, userName, avatar, isMaster} = options
  
  let roomStore = useRoomtore();
  let chatStore = useChatStore();

  async function joinGroup() {
    return await toAwait(timInstance.joinGroup({
      groupID: roomId,
    }))
  }
  // async function quitGroup() {
  //   return await toAwait(timInstance.quitGroup(roomId))
  // }
  async function createGroup() {
    const options = {
      name: roomId,
      groupID: roomId,
      type: TIM.TYPES.GRP_PUBLIC,
      joinOption: TIM.TYPES.JOIN_OPTIONS_FREE_ACCESS,
    };
    return await toAwait(timInstance.createGroup(options))
  }
  let err
  [err] = await toAwait(timInstance.login({
    userID: userId,
    userSig: userSig,
  }));
  if (err) {
    throw(err)
  }

  await new Promise((resolve) => {
    timInstance.on(TIM.EVENT.SDK_READY, () => {
      resolve(true)
    });
  })
  
  chatStore.setSdkReady(true)

  timInstance.updateMyProfile({
    nick: userName,
    avatar: avatar
  })

  let error = null
  if(isMaster) {
    [error] = await toAwait(timInstance.searchGroupByID(roomId))
  }
  if (error?.code === 10015 || error?.code === 10010) {
    [err] = await createGroup();
    if (err) {
      throw(err)
    }
  } else {
    [err] = await joinGroup();
    if (err && err?.code !== 10013) {
      throw(err)
    }
  }

  chatStore.setTimReady(true)
  
  let promise = timInstance.getGroupMemberProfile({
    userIDList: [userId],
    groupID: roomId,
  });
  promise.then(function(imResponse) {
    const [{muteUntil}] = imResponse.data?.memberList
    const isMessageDisabled = muteUntil > +new Date() / 1000

    if (isMessageDisabled) {
      chatStore.setSendMessageDisableChanged(isMessageDisabled);
      roomStore.setMuteUserChat(userId, isMessageDisabled);
    }
  }).catch(function(imError) {
    console.warn('getMyProfile error:', imError);
  });
}

export default TIM

export let timInstance: ChatSDK

export function createTIM() {
  if(!timInstance) {
    let basicStore = useBasicStore();
    timInstance = TIM.create({
      SDKAppID: basicStore.sdkAppId
    });
    timInstance.setLogLevel(1);
  }
  return timInstance
}
