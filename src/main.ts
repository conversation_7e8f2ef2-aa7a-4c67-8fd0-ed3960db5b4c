import { createApp } from 'vue';
import { createPinia } from 'pinia';
import TRTC from '@/services/trtc';
import App from './App.vue';
import router from './router';
import { statInit, sendWarning, errorToObject }  from '@/utils/utils';

import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import '@mdi/font/css/materialdesignicons.css'; // Ensure you are using css-loader
import 'material-design-icons-iconfont/dist/material-design-icons.css';
import { aliases, mdi } from 'vuetify/iconsets/mdi'
import './style.scss'

const vuetify = createVuetify({
  theme: {
    themes: {
      dark: {
        dark: false,
        colors: {
          'mc-bule-darken-1': '#10102A',
          'mc-bule-darken-2': '#1D224C',
          'mc-bule-darken-3': '#141734',
          'mc-bule-darken-4': '#273352',
          'mc-bule-darken-5': '#0A4282',
          'mc-bule-darken-6': '#171b39',
          'mc-bule-darken-7': '#11112f',
          'mc-bule-darken-8': '#0e0c19',
          'mc-bule-darken-9': '#111229',
          'mc-bule-lighten-1': '#0087FA',
          'mc-red-darken-1': '#BB2D37',
          'mc-green-lighten-1': '#56EEAB',
          'mc-green-lighten-2': '#4FCD95',
          'mc-pink-lighten-1': '#EC85D8',
          'mc-gray-lighten-1': '#9FA3B2',
          'mc-orange-lighten-1': '#FFA70F',
        }
      },
      light: {
        dark: false,
        colors: {
          'mc-bule-lighten-1': '#04A5FF',
        }
      },
    },
  },
  defaults: {
    VBtn: {
      variant: 'text'
    },
    VTextField: {
      'singleLine': true,
      variant: 'outlined',
      density: 'compact'
    },
    VSelect: {
      'singleLine': true,
      variant: 'outlined',
      density: 'comfortable'
    }
  },
  icons: {
    defaultSet: 'mdi',
    aliases,
    sets: {
      mdi,
    }
  }
})

statInit()

const app = createApp(App);

if (!import.meta.env.DEV) {
  app.config.errorHandler = (err, _instance, info) => {
    sendWarning(1, {
      info,
      content: errorToObject(err as Error)
    })
  }
}
TRTC.setLogLevel(2);
app.use(router);
app.use(createPinia());
app.use(vuetify);
app.mount('#app');
