
import { ref } from 'vue';
import {throttle} from 'lodash-es'

const width = ref(0)
const height = ref(0)
const left = ref(0)
const bottom = ref(0)

function setMainSize() {
  const main = document.documentElement
  const {clientWidth} = main!
  let {offsetWidth, offsetHeight} = main!
  offsetHeight = offsetHeight - 72 - 104
  const radio = offsetWidth / offsetHeight
  const defaultRadio = 16 / 9
  if (defaultRadio > radio) {
    offsetHeight = offsetWidth / defaultRadio
  } else {
    offsetWidth = offsetHeight * defaultRadio
  }
  width.value = offsetWidth
  height.value = offsetHeight
  left.value = (clientWidth - offsetWidth) / 2
  bottom.value = 104
}
const handleResize = throttle(setMainSize, 60);
window.addEventListener('resize', handleResize);

export default function () {
  setMainSize()
  return {
    width,
    height,
    left,
    bottom
  };
};
