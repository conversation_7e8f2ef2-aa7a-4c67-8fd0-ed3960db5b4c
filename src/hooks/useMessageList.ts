import { onMounted, onUnmounted, nextTick, watch, ref } from 'vue';
import {throttle} from 'lodash-es'
import { storeToRefs } from 'pinia';
import TIM, { timInstance } from '@/services/tim';
import {Message} from 'tim-js-sdk'
import useBasicStore from '@/store/basic';
import useChatStore from '@/store/chat';

interface IScrollInfo {
	scrollHeight: number;
	scrollLeft: number;
	scrollTop: number;
	scrollWidth: number;
	id: string;
}

function getScrollInfo(selector: HTMLElement | null): Promise<IScrollInfo> {
	if (!selector) {
		return Promise.reject(new Error(`getScrollInfo() get error selector ${typeof selector}.`));
	}

	return Promise.resolve({
		id: selector.id,
		scrollTop: selector.scrollTop,
		scrollLeft: selector.scrollLeft,
		scrollWidth: selector.scrollWidth,
		scrollHeight: selector.scrollHeight
	});
}

export default function (messageListId: string) {
  const chatStore = useChatStore();
  const { messageList } = storeToRefs(chatStore);
  const basicStore = useBasicStore();
  const { roomId } = storeToRefs(basicStore);
  let scrollTop = ref(0)

  async function setMessageListInfo() {
    const { currentMessageList, isCompleted, nextReqMessageId } = await getMessageList();
    chatStore.setMessageListInfo(currentMessageList, isCompleted, nextReqMessageId);
    // await nextTick()
    // scrollToLatestMessage()
  }
  
  async function getMessageList(): Promise<{
    currentMessageList: any[];
    isCompleted: boolean,
    nextReqMessageId: string,
  }> {
    let count = 0;
    const result: {
      currentMessageList: any[],
      isCompleted: boolean,
      nextReqMessageId: string,
    } = {
      currentMessageList: [],
      isCompleted: false,
      nextReqMessageId: '',
    };
    const getIMMessageList = async () => {
      const conversationData: {
        conversationID: string,
        nextReqMessageID?: string | undefined;
      } = {
        conversationID: `GROUP${roomId.value}`,
      };
      if (result.nextReqMessageId !== '') {
        conversationData.nextReqMessageID = result.nextReqMessageId;
      }
      const imResponse = await timInstance.getMessageList(conversationData);
      const { messageList, isCompleted, nextReqMessageID } = imResponse.data;
      const filterCurrentMessageList = messageList.filter((item: any) => item.type === TIM.TYPES.MSG_TEXT);
      result.currentMessageList.splice(0, 0, ...filterCurrentMessageList);
      result.isCompleted = messageList.length > 0 ? isCompleted : true;
      result.nextReqMessageId = nextReqMessageID;
      if (result.isCompleted || result.currentMessageList.length >= 15) {
        return;
      }
      count += 1;
      if (count <= 5) {
        await getIMMessageList();
      }
    };

    await getIMMessageList();

    return result;
  };

  function onReceiveMessage(event: { data: Message[] }) {
    const messages = event.data;

    messages.forEach((message) => {
      // 群组消息
      if (message.type === TIM.TYPES.MSG_TEXT && message.to === roomId.value) {
        const { ID, payload: { text }, nick: userName, from: userId, avatar } = message;
        chatStore.updateMessageList({
          ID,
          type: TIM.TYPES.MSG_TEXT as string,
          payload: {
            text,
          },
          avatar,
          nick: userName || userId,
          from: userId,
          flow: userId === basicStore.userId ? 'out' : 'in',
          sequence: Math.random(),
        });
      }
    });
  }
  timInstance?.on(TIM.EVENT.MESSAGE_RECEIVED, onReceiveMessage);

  let isScrollAtBottom = true;
  const handleMessageListScroll = (e: Event) => {
    const messageContainer = e.target as HTMLElement;
    const bottom = messageContainer.scrollHeight - messageContainer.scrollTop - messageContainer.clientHeight;
    isScrollAtBottom = bottom <= 80;
    scrollTop.value = messageContainer.scrollTop
  };
  const handleScroll = throttle(handleMessageListScroll, 1000);

  onMounted(() => {
    const messageListRef = document.getElementById(messageListId)
    if (messageListRef) {
      messageListRef.addEventListener('scroll', handleScroll);
    }
  })

  onUnmounted(() => {
    const messageListRef = document.getElementById(messageListId)
    if (messageListRef) {
      messageListRef.removeEventListener('scroll', handleScroll);
    }
  })

  async function scrollToLatestMessage() {
    const messageListRef = document.getElementById(messageListId)
    const { scrollHeight } = await getScrollInfo(messageListRef);
    if (messageListRef) {
      messageListRef.scrollTop = scrollHeight;
    }
  }

  watch(
    messageList,
    async (newMessageList, oldMessageList) => {
      if ((newMessageList as any).length === 0) {
        return;
      }
      const lastMessage = (newMessageList as any).slice(-1);
      const oldLastMessage = (oldMessageList as any).slice(-1);
      const isSendByMe = lastMessage[0].flow === 'out';
      const isNewMessage = lastMessage[0].ID !== oldLastMessage[0]?.ID;
      await nextTick();
      if (isScrollAtBottom) {
        scrollToLatestMessage();
        return;
      }
      if (isSendByMe && isNewMessage) {
        scrollToLatestMessage();
      }
      scrollToLatestMessage();
    },
    { deep: true }
  );

  return {
    setMessageListInfo,
    messageList,
    scrollTop
  };
};
