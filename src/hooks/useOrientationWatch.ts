
import { ref } from 'vue';

let isLandscape = ref(Math.abs(window.orientation) === 90)
window.addEventListener('resize', function () {
  isLandscape.value = Math.abs(window.orientation) === 90
  let i = 0
  let timer = setInterval(() => {
    isLandscape.value = Math.abs(window.orientation) === 90
    if (i === 10) {
      clearInterval(timer)
    }
    i++
  }, 100)
});

export default function () {

  function getLandscape() {
    isLandscape.value = Math.abs(window.orientation) === 90
    return isLandscape.value
  }

  return {
    getLandscape,
    isLandscape
  };
};
