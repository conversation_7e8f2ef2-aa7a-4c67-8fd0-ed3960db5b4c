import { watch, getCurrentInstance } from 'vue';
import { trtcInstance, enterRoom, exitRoom, getUserRole } from '@/services/trtc';
import TIM, { createTIM, timInstance, initGroup } from '@/services/tim';
import TUIMessage from '@/components/TUI/Message/index';
import TUIMessageBox from '@/components/TUI/MessageBox/index';
import { httpRequest, URLParams, toAwait, isMucang, compareVersionWithBizCode, trackEvent } from '@/utils/utils';
import useBasicStore from '@/store/basic';
import useRoomStore from '@/store/room';
import useChatStore from '@/store/chat';
import {OPERATETYPE} from '@/types/type'

export default function () {
  const { appContext } = getCurrentInstance()!
  const basicStore = useBasicStore();
  const roomStore = useRoomStore();
  const chatStore = useChatStore();
  watch(() => roomStore.currentMicrophoneId, async () => {
    if (roomStore.currentMicrophoneId) {
      if (roomStore.localUser.hasAudioStream) {
        const [err] = await toAwait(trtcInstance.updateLocalAudio({
          option: {
            microphoneId: roomStore.currentMicrophoneId,
          },
        }));
        if (err) {
          TUIMessage({
            type: 'error',
            message: err.message || '设置失败',
            duration: 3000,
            appContext
          })
        }
        return
      }
      const [err] = await toAwait(trtcInstance.startLocalAudio({
        option: {
          microphoneId: roomStore.currentMicrophoneId,
        },
      }));
      if (err) {
        const alertMessage = '麦克风权限未开启，无法进行语音通话。';
        const t = compareVersionWithBizCode('8.67.0') >= 0;
        if (isMucang && t) {
          TUIMessageBox({
            title: '提示',
            message: alertMessage,
            confirmButtonText: '前往开启',
            cancelButtonText: '关闭',
            callback: async (result) => {
              if (result) {
                location.href = 'http://system.nav.mucang.cn/openAppSetting'
              }
            },
            appContext
          });
        } else {
          TUIMessageBox({
            title: '提示',
            message: alertMessage,
            confirmButtonText: '好的',
            appContext
          });
        }
        roomStore.setCurrentMicrophoneId('')
      } else {
        roomStore.setLocalUser({hasAudioStream: true});
      }
    }
  })
  watch(() => basicStore.userSig, async (val) => {
    if (val) {
      const userRole = getUserRole(basicStore.userId)
      roomStore.setLocalUser({userId: basicStore.userId, userName: basicStore.userName, avatarUrl: basicStore.avatar, userRole});
      joinTim();
    }
  })
  watch(() => roomStore.status, async (val) => {
    if (val === 2 && !roomStore.isTrtcReady) {
      handleEnter(false)
    }
  })
  // watch(() => roomStore.videoDeviceId, async () => {
  //   await trtcInstance.startLocalVideo({
  //     view: 'video-preview',
  //     option: {
  //       cameraId: roomStore.videoDeviceId,
  //       profile: '1080p',
  //       fillMode: 'contain'
  //     },
  //   });
  //   roomStore.setLocalUser({hasVideoStream: true});
  // })
  
  // function saveRoomInfo() {
  //   const {sdkAppId, userId, roomId, userSig} = basicStore
  //   const roomInfo = {sdkAppId, userId, roomId, userSig}
  //   sessionStorage.setItem('personal-training-basicInfo', JSON.stringify(roomInfo));
  // }

  // 每次延长 min
  // 提前预警 min
  const perTime = 30
  const warnTime = 5
  const times = 3
  let endRoomTimer: NodeJS.Timeout
  function startCourseTimeCheck() {
    const nowTime = +new Date();
    const saveEndTime = Number(localStorage.getItem(`endTime-${URLParams.courseId}`));
    const serverTime = basicStore.serverTime
    const localTime = basicStore.localTime
    const endTime = Math.max(basicStore.endTime, saveEndTime)
    // localStorage.setItem(`endTime-${URLParams.courseId}`, basicStore.endTime);
    const gap = nowTime - localTime
    const countdown = (endTime - serverTime) - gap
    if (countdown > 0 && saveEndTime < basicStore.endTime + perTime*60*1000 * times) {
      localStorage.setItem(`endTime-${URLParams.courseId}`, String(endTime));
      setTimeout(() => {
        TUIMessageBox({
          title: '提示',
          message: `距离下课时间不足${warnTime}分钟，是否需要延长授课时间？`,
          confirmButtonText: `延长${perTime}分钟`,
          cancelButtonText: '取消',
          callback: async (result) => {
            if (result) {
              localStorage.setItem(`endTime-${URLParams.courseId}`, String(endTime + perTime*60*1000));
              clearTimeout(endRoomTimer)
              startCourseTimeCheck()
            }

            trackEvent({
              fragmentName1: '下课提醒弹窗',
              actionType: '点击',
              actionName: result ? '延长' : '取消',
            })
          },
          closeInMask: false,
          appContext
        });
      }, countdown - warnTime*60*1000)
      endRoomTimer = setTimeout(() => {
        handleExit()
      }, countdown)
    } else {
      endRoomTimer = setTimeout(() => {
        handleExit()
      }, countdown)
    }
  }

  async function sendRoomStatusChange() {
    if (!timInstance) return;
    const groupId = String(basicStore.roomId);
    const message = timInstance.createCustomMessage({
      to: groupId,
      conversationType: TIM.TYPES.CONV_GROUP,
      priority: TIM.TYPES.MSG_PRIORITY_NORMAL,
      payload: {
        data: JSON.stringify({
          operateType: OPERATETYPE.ROOMSTATUSUPDATE,
        }),
        description: '',
        extension: 'MCTrainingLiveExt',
      },
    });
    timInstance.sendMessage(message, {
      messageControlInfo: {
        excludedFromContentModeration: true
      }
    })
  }
  
  async function joinTim() {
    createTIM()
    const [err] = await toAwait(initGroup({
      userName: basicStore.userName,
      avatar: basicStore.avatar,
      roomId: basicStore.roomId,
      userId: basicStore.userId,
      userSig: basicStore.userSig,
      isMaster: roomStore.isMaster
    }))
    if (err) {
      let message = `TIM SDK出现错误，code:${err.code}`
      if (err.code === 10015) {
        message = '课程还未开始，请稍后再来'
      }
      TUIMessage({
        type: 'error',
        message,
        duration: 3000,
        appContext
      })
    }
  }
  
  async function handleEnter(isPush: boolean) {
    try {
      await enterRoom()
      if (basicStore.roomNo && roomStore.isMaster && isPush) {
        if (!chatStore.isTimReady) {
          TUIMessageBox({
            title: '提示',
            message: 'IM 初始化失败，无法开始上课',
            confirmButtonText: '确定',
            appContext
          });
          return
        }
        const [err] =  await toAwait(httpRequest({
          url: 'api/web/teaching-room/start-teach.htm',
          params: {
            roomNo: basicStore.roomNo,
            width: 1280,
            height: 720
          },
          host: 'parrot',
        }))
        if (err) {
          TUIMessageBox({
            title: '提示',
            message: '白板推流失败',
            confirmButtonText: '确定',
            appContext
          });
          return
        }
        sendRoomStatusChange()
      }
      roomStore.setRoomStatus(2)
  
      if (roomStore.isMaster) {
        startCourseTimeCheck()
      }
  
      // saveRoomInfo()
    } catch (error: any) {
    }
  }
  
  async function handleExit() {
    try {
      await exitRoom()
      roomStore.setRoomStatus(3)
  
      if (basicStore.roomNo) {
        await httpRequest({
          url: 'api/web/teaching-room/stop-teach.htm',
          params: {
            roomNo: basicStore.roomNo
          },
          host: 'parrot',
        })
        sendRoomStatusChange()
      }
  
      sessionStorage.removeItem('personal-training-basicInfo')
      // todo
    } catch (error: any) {
    }
  }
    
  return {
    handleEnter,
    handleExit
  };
}