import { onMounted, onUnmounted, getCurrentInstance, watch } from 'vue';
import TRTC, { trtcInstance } from '@/services/trtc';
import TIM, { timInstance } from '@/services/tim';
import {Message} from 'tim-js-sdk'
import TUIMessageBox from '@/components/TUI/MessageBox/index';
import TUIMessage from '@/components/TUI/Message/index';
import useBasicStore from '@/store/basic';
import useRoomStore from '@/store/room';
import useChatStore from '@/store/chat';
import {DEVICETYPE, OPERATETYPE} from '@/types/type'
import { isMucang, compareVersionWithBizCode } from '@/utils/utils';

let isAuthorizeAccept = false

export default function () {
  const { appContext } = getCurrentInstance()!
  const basicStore = useBasicStore();
  const roomStore = useRoomStore();
  const chatStore = useChatStore();

  async function initMediaDeviceList() {

    if (!isAuthorizeAccept) {
      await new Promise((resolve) => {
        TUIMessageBox({
          title: '提示',
          message: '开启麦克风权限用于语音通话。',
          confirmButtonText: '打开麦克风',
          cancelButtonText: '取消',
          callback: async (result) => {
            if (result) {
              resolve(true)
            }
          },
          appContext
        });
      })
      isAuthorizeAccept = true
    }

    // const cameraList: DeviceItem[] = await TRTC.getCameraList();
    let microphoneList = await TRTC.getMicrophoneList()
    let speakerList = await TRTC.getSpeakerList()

    // const hasCameraDevice = cameraList && cameraList.length > 0;
    const hasMicrophoneDevice = microphoneList && microphoneList.length > 0 && microphoneList[0].deviceId;
    let alertMessage = '';
    if (!hasMicrophoneDevice) {
      alertMessage = '麦克风权限未开启，无法进行语音通话。';
    }
    // else if (!hasCameraDevice && hasMicrophoneDevice) {
    //   alertMessage = '当前设备未检测到摄像头';
    // }
    // else if (!hasCameraDevice && !hasMicrophoneDevice) {
    //   alertMessage = '当前设备未检测到摄像头和麦克风';
    // }

    if (alertMessage) {
      const t = compareVersionWithBizCode('8.67.0') >= 0;
      if (isMucang && t) {
        TUIMessageBox({
          title: '提示',
          message: alertMessage,
          confirmButtonText: '前往开启',
          cancelButtonText: '关闭',
          callback: async (result) => {
            if (result) {
              location.href = 'http://system.nav.mucang.cn/openAppSetting'
            }
          },
          appContext
        });
      } else {
        TUIMessageBox({
          title: '提示',
          message: alertMessage,
          confirmButtonText: '好的',
          appContext
        });
      }
    }

    // cameraList && roomStore.setCameraList(cameraList);
    microphoneList && roomStore.setMicrophoneList(microphoneList);
    speakerList && roomStore.setSpeakerList(speakerList);

  }

  function initCommand() {
    watch(
      () => chatStore.isTimReady,
      (val) => {
        if (val) {
          timInstance.on(TIM.EVENT.MESSAGE_RECEIVED, (event: { data: Message[] }) => {
            const messages = event.data;
        
            messages.forEach(async (message) => {
              // if (message.type === TIM.TYPES.MSG_GRP_TIP) {
              //   const { payload: { operationType } } = message;
              //   if (operationType === TIM.TYPES.GRP_TIP_MBR_PROFILE_UPDATED) {
              //     const { payload: {memberList: [{userID, muteTime}]} } = message;
              //     const isDisable = muteTime > 0
              //     if (basicStore.userId === userID) {
              //       chatStore.setSendMessageDisableChanged(isDisable);
    
              //       const tipMessage = isDisable ? '您被禁止文字聊天' : '您被允许文字聊天';
              //       TUIMessage({
              //         type: 'warning',
              //         message: tipMessage,
              //         duration: 3000,
              //         appContext
              //       });
              //     }
              //     roomStore.setMuteUserChat(userID, isDisable);
              //   }
              // } else
              if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
                const { payload: { operationType } } = message;
                if (operationType === 16) {
                  const { payload: {groupProfile: {to :userID}, muteTime} } = message;
                  const isDisable = muteTime > 0
                  if (basicStore.userId === userID) {
                    chatStore.setSendMessageDisableChanged(isDisable);
    
                    const tipMessage = isDisable ? '您被禁止文字聊天' : '您被允许文字聊天';
                    TUIMessage({
                      type: 'warning',
                      message: tipMessage,
                      duration: 3000,
                      appContext
                    });
                  }
                  roomStore.setMuteUserChat(userID, isDisable);
                }
              } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
                const { payload: {data} } = message;
                const messageData = JSON.parse(data || '{}')
                const {to: userID, deviceType, operateType} = messageData
                if (basicStore.userId === userID) {
                  if (deviceType === DEVICETYPE.MICROPHONE) {
                    if (operateType === OPERATETYPE.CLOSEDEVICE) {
                      // onUserAudioStateChanged
                      await trtcInstance.updateLocalAudio({ mute: true });
                      roomStore.setLocalUser({hasAudioStream: false});
                      TUIMessage({
                        type: 'warning',
                        message: '已关闭您的麦克风',
                        duration: 3000,
                        appContext
                      });
        
                    } else if (operateType === OPERATETYPE.OPENDEVICE) {
                      // onRequestReceived
                      TUIMessageBox({
                        title: '提示',
                        message: '主持人邀请你打开麦克风',
                        confirmButtonText: '打开麦克风',
                        cancelButtonText: '保持关闭',
                        callback: async (result) => {
                          if (result) {
                            await trtcInstance.updateLocalAudio({ mute: false });
                            roomStore.setLocalUser({hasAudioStream: true});
                          }
                        },
                        appContext
                      });
                    }
                  }
                } else if (operateType === OPERATETYPE.WARNMASTER && roomStore.isMaster) {
                  await trtcInstance.updateLocalAudio({ mute: true });
                  roomStore.setLocalUser({hasAudioStream: false});
                  TUIMessageBox({
                    title: '警告',
                    message: '管理员已关闭您的麦克风，请勿在课堂上进行任何与授课无关的行为，多次违规将会取消您的讲师资格。',
                    confirmButtonText: '确定',
                    appContext
                  });
                }
              }
            });
          });
        }
      }, {
        immediate: true
      }
    )
  }
  async function onDeviceChanged() {
    // const cameraList: MediaDeviceInfo[] = await TRTC.getCameraList();
    const microphoneList: MediaDeviceInfo[] = await TRTC.getMicrophoneList();
    let speakerList = await TRTC.getSpeakerList();

    // cameraList && roomStore.setCameraList(cameraList);
    microphoneList && roomStore.setMicrophoneList(microphoneList);
    speakerList && roomStore.setSpeakerList(speakerList);
  }

  function getCurrentDevice(type: string) {
    if (type === 'camera') {
      return roomStore.currentCameraId
    } else if (type === 'micphone') {
      return roomStore.currentMicrophoneId
    } else if (type === 'speaker') {
      return roomStore.currentSpeakerId
    }
  }

  onMounted(() => {
    navigator.mediaDevices.ondevicechange = onDeviceChanged;
  });
  onUnmounted(() => {
    navigator.mediaDevices.ondevicechange = null;
  });
    
  return {
    initMediaDeviceList,
    initCommand,
    getCurrentDevice
  };
}