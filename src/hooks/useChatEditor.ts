
import { ref, watch, getCurrentInstance } from 'vue';
import { storeToRefs } from 'pinia';
import TUIMessage from '@/components/TUI/Message/index';
// import TIM, { timInstance } from '@/services/tim';
import useBasicStore from '@/store/basic';
// import useRoomStore from '@/store/room';
import useChatStore from '@/store/chat';
import { toAwait, sendWarning, errorToObject, httpRequest } from '@/utils/utils';

async function sendMsg(params: {
  roomNo: string,
  content: string
}) {
  return await httpRequest({
    url: 'api/web/teaching-room/send-msg.htm',
    params,
    host: 'parrot',
  })
}

export default function () {
  const { appContext } = getCurrentInstance()!
  const chatStore = useChatStore();
  // const roomStore = useRoomStore();
  const basicStore = useBasicStore();

  const { isMessageDisabled } = storeToRefs(chatStore);
  let sendIng = ref(false)
  let text = ref('')
  watch(isMessageDisabled, (value) => {
    if (value) {
      text.value = '';
    }
  });
  async function sendMessage(quickText?: string) {
    if (sendIng.value) return
    const result = quickText || text.value;
    // let message = timInstance.createTextMessage({
    //   to: basicStore.roomId,
    //   conversationType: TIM.TYPES.CONV_GROUP,
    //   payload: {
    //     text: result
    //   },
    // });
    if (!result) return
    sendIng.value = true
    const [err] = await toAwait(sendMsg({
      roomNo: basicStore.roomNo,
      content: result,
    }))
    sendIng.value = false
    if(err) {
      const errorMsg = err.message || `发送失败，请稍后重试 ${err.code || ''}`
      TUIMessage({
        type: 'error',
        message: errorMsg,
        duration: 3000,
        appContext
      })
      sendWarning(3, {
        message: {
          to: basicStore.roomId,
          // conversationType: TIM.TYPES.CONV_GROUP,
          payload: {
            text: result
          },
        },
        content: errorToObject(err as Error)
      })
      return
    }
    if (!quickText) {
      text.value = ''
    }
    // const {avatarUrl, userName, userId} = roomStore.localUser
    // chatStore.updateMessageList({
    //   ID: Math.random().toString(),
    //   type: TIM.TYPES.MSG_TEXT as string,
    //   payload: {
    //     text: result,
    //   },
    //   avatar: avatarUrl,
    //   nick: userName || userId,
    //   from: userId,
    //   flow: 'out',
    //   sequence: Math.random(),
    // });
    console.log('sendMessage done')
  }

  return {
    text,
    isMessageDisabled,
    sendMessage
  };
};
