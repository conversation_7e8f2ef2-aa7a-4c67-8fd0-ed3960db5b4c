
import { watch } from 'vue';
import TIM, { timInstance } from '@/services/tim';
import {Message} from 'tim-js-sdk'
import useBasicStore from '@/store/basic';
import useChatStore from '@/store/chat';
import {OPERATETYPE} from '@/types/type'
import {QuestionMap} from '@/utils/question'
import { trackEvent } from '@/utils/utils';

export default function () {
  const basicStore = useBasicStore();
  const chatStore = useChatStore();

  function setActionStatus(courseId: string, actionStatus: string) {
    localStorage.setItem(
      `examRecord-${courseId}`,
      JSON.stringify({
        actionStatus,
      })
    );
  }

  function getActionStatus(courseId: string) {
    let saveExamInfo = JSON.parse(localStorage.getItem(`examRecord-${courseId}`) || '{}');
    return saveExamInfo.actionStatus
  }

  function showExamDialog(courseId: string) {
    let saveExamInfo = JSON.parse(localStorage.getItem(`examInfo-${courseId}`) || '{}');
    if (saveExamInfo.questionList?.length) {
      const done = saveExamInfo?.questionList.every((item: QuestionMap) => {
        return item.isCorrent > 1
      })
      return !done
    }
    return false
  }

  async function sendStartExam() {
    if (!timInstance) return;
    const groupId = String(basicStore.roomId);
    const message = timInstance.createCustomMessage({
      to: groupId,
      conversationType: TIM.TYPES.CONV_GROUP,
      priority: TIM.TYPES.MSG_PRIORITY_NORMAL,
      payload: {
        data: JSON.stringify({
          operateType: OPERATETYPE.STARTEXAM,
        }),
        description: '',
        extension: 'MCTrainingLiveExt',
      },
    });
    timInstance.sendMessage(message, {
      messageControlInfo: {
        excludedFromContentModeration: true
      }
    })
  }

  function onStartExam(callback: Function) {
    if (!callback) return
    watch(
      () => chatStore.isTimReady,
      (val) => {
        if (val) {
          timInstance.on(TIM.EVENT.MESSAGE_RECEIVED, (event: { data: Message[] }) => {
            const messages = event.data;

            messages.forEach(async (message) => {
              if (message.type === TIM.TYPES.MSG_CUSTOM) {
                const { payload: {data} } = message;
                const messageData = JSON.parse(data || '{}')
                const {operateType} = messageData
                if (operateType === OPERATETYPE.STARTEXAM) {
                  callback()
                }
              }
            });
          });
        }
      }, {
        immediate: true
      }
    )
  }

  async function sendSubmitExam() {
    if (!timInstance) return;
    const groupId = String(basicStore.roomId);
    const message = timInstance.createCustomMessage({
      to: groupId,
      conversationType: TIM.TYPES.CONV_GROUP,
      priority: TIM.TYPES.MSG_PRIORITY_NORMAL,
      payload: {
        data: JSON.stringify({
          operateType: OPERATETYPE.SUBMITEXAM,
        }),
        description: '',
        extension: 'MCTrainingLiveExt',
      },
    });
    timInstance.sendMessage(message, {
      messageControlInfo: {
        excludedFromContentModeration: true
      }
    })
  }

  function onSubmitExam(callback: Function) {
    if (!callback) return
    watch(
      () => chatStore.isTimReady,
      (val) => {
        if (val) {
          timInstance.on(TIM.EVENT.MESSAGE_RECEIVED, (event: { data: Message[] }) => {
            const messages = event.data;

            messages.forEach(async (message) => {
              if (message.type === TIM.TYPES.MSG_CUSTOM) {
                const { payload: {data} } = message;
                const messageData = JSON.parse(data || '{}')
                const {operateType} = messageData
                if (operateType === OPERATETYPE.SUBMITEXAM) {
                  callback()
                  trackEvent({
                    actionType: '收到',
                    actionName: '随堂测验提交消息',
                    content: JSON.stringify(message),
                  });
                }
              }
            });
          });
        }
      }, {
        immediate: true
      }
    )
  }

  return {
    setActionStatus,
    getActionStatus,
    showExamDialog,
    sendStartExam,
    onStartExam,
    sendSubmitExam,
    onSubmitExam
  };
};
