!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.mcLogin=n():e.mcLogin=n()}(window,(function(){return function(e){var n={};function t(o){if(n[o])return n[o].exports;var i=n[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,t),i.l=!0,i.exports}return t.m=e,t.c=n,t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:o})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(t.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var i in e)t.d(o,i,function(n){return e[n]}.bind(null,i));return o},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=6)}([function(e,n,t){(n=t(4)(!1)).push([e.i,".com-verify-code {\n  position: fixed;\n  z-index: 99;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n.com-verify-code .com-verify-conent {\n  background: #fff;\n  width: 315px;\n  height: 300px;\n  border-radius: 15px;\n  padding: 16px 18px 24px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n.com-verify-code .com-verify-conent .header {\n  font-size: 18px;\n  font-weight: bold;\n  text-align: center;\n}\n.com-verify-code .com-verify-conent .tip {\n  font-size: 12px;\n  color: #D23104;\n  margin-top: 4px;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt {\n  margin-top: 8px;\n  display: flex;\n  background-size: 20px 22px;\n  background-position: left center;\n  background-repeat: no-repeat;\n  justify-content: space-between;\n  width: 249px;\n  height: 44px;\n  border-radius: 2px;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt input {\n  outline: none;\n  border: none;\n  background: #f7f7f7;\n  font-size: 15px;\n  padding-left: 12px;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt .phone {\n  width: 100%;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt .yzm {\n  width: 138px;\n  height: 44px;\n  border-radius: 2px;\n  border: none;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt .djs {\n  width: 99px;\n  height: 44px;\n  border-radius: 2px;\n  border: 1px solid #F4C3A9;\n  font-size: 14px;\n  font-weight: 400;\n  color: #ED631C;\n  line-height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #FFEEE6;\n  border-radius: 4px;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt .disabeld {\n  background: #F4F7F7;\n  border: none !important;\n  color: #999 !important;\n}\n.com-verify-code .com-verify-conent .text {\n  margin-top: 15px;\n}\n.com-verify-code .com-verify-conent .text p {\n  font-size: 12px;\n  font-weight: 400;\n  color: #9F5217;\n  line-height: 17px;\n  margin-bottom: 2px;\n}\n.com-verify-code .com-verify-conent .text p a {\n  font-size: 12px;\n  font-family: PingFangSC-Regular, PingFang SC;\n  font-weight: 400;\n  color: #04A5FF;\n  line-height: 17px;\n}\n.com-verify-code .com-verify-conent .com-verify-btn {\n  width: 249px;\n  height: 40px;\n  line-height: 40px;\n  box-shadow: 0px 4px 12px -4px rgba(254, 89, 89, 0.5);\n  background: linear-gradient(135deg, #FCC6A6 0%, #FDA789 100%);\n  border-radius: 25px;\n  font-size: 16px;\n  font-weight: 500;\n  color: #FFFFFF;\n  text-align: center;\n  margin-top: 16px;\n}\n.com-verify-code .com-verify-conent .activied {\n  background: linear-gradient(135deg, #FF7900 0%, #FF1C54 100%);\n}\n.com-verify-code .df-close {\n  margin-left: -21/2px;\n  width: 50px;\n  height: 50px;\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEIAAABACAMAAACZQlHRAAAAM1BMVEUAAAD///////////////////////////////////////////////////////////////+3leKCAAAAEHRSTlMAgDHsbHcIQVMZSCtgETokMbaimQAAAahJREFUWMO1mO12wyAIho0F1CQmuf+rnR/bmnqUJudt+bWhPiJghJqhyOI8EU8TE3m3iLkpYaapEZrD9fWy0dQV2uQawHFdsDoboiRFDNatVcfuAsQWAPu9Hdh9HbFvALHsRnaAp2Jd1AgLZ8CiTMgQVibM5bS6ncVT82jUZxPC23hnQ3x/bM1DciFkPjuka0NroH5g31c7c1Fcb7ulp9TtaOISeeAixfUcW1eS3EEINS61iRlu3uVktz0hWXGl4lKW839kbgud9hVu3Ut2cM1eg/g0Y2uN4ONhe4THwY0Z2/PPlxXhcfQYNqvDWfG/dUgGdSYXhq5MDgh/meaV6QrWl4yu59jNgKEbtv+eRFK6G6MwuoQqaanU2KxGYSgEs9ZscDlDhgyF8Fzrc0gVhkIwtkaCcmQUhkLI+UAVEY3CWMcEEyuCs1cVxjEmGKlZWWKqMBRCXYwjPnMQ3J14UPHU+kyC49cMv+z4Jwf/8H3o84s/AvhThD+I+LOMFwd4iYIXSni5hheNeOmKF9B4GY83E3hLgzdWeHsHN5l4qws33F9q+/EfH34Az/UURYgKgFQAAAAASUVORK5CYII=) no-repeat center center;\n  background-size: 60%;\n  position: static;\n  margin-top: 10px;\n}\n.mark126 {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 1005;\n}\n.mark126 .captcha {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  background: white;\n}\n",""]),e.exports=n},function(e,n,t){"use strict";var o,i=function(){return void 0===o&&(o=Boolean(window&&document&&document.all&&!window.atob)),o},r=function(){var e={};return function(n){if(void 0===e[n]){var t=document.querySelector(n);if(window.HTMLIFrameElement&&t instanceof window.HTMLIFrameElement)try{t=t.contentDocument.head}catch(e){t=null}e[n]=t}return e[n]}}(),a=[];function c(e){for(var n=-1,t=0;t<a.length;t++)if(a[t].identifier===e){n=t;break}return n}function s(e,n){for(var t={},o=[],i=0;i<e.length;i++){var r=e[i],s=n.base?r[0]+n.base:r[0],d=t[s]||0,u="".concat(s," ").concat(d);t[s]=d+1;var l=c(u),f={css:r[1],media:r[2],sourceMap:r[3]};-1!==l?(a[l].references++,a[l].updater(f)):a.push({identifier:u,updater:v(f,n),references:1}),o.push(u)}return o}function d(e){var n=document.createElement("style"),o=e.attributes||{};if(void 0===o.nonce){var i=t.nc;i&&(o.nonce=i)}if(Object.keys(o).forEach((function(e){n.setAttribute(e,o[e])})),"function"==typeof e.insert)e.insert(n);else{var a=r(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(n)}return n}var u,l=(u=[],function(e,n){return u[e]=n,u.filter(Boolean).join("\n")});function f(e,n,t,o){var i=t?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(e.styleSheet)e.styleSheet.cssText=l(n,i);else{var r=document.createTextNode(i),a=e.childNodes;a[n]&&e.removeChild(a[n]),a.length?e.insertBefore(r,a[n]):e.appendChild(r)}}function p(e,n,t){var o=t.css,i=t.media,r=t.sourceMap;if(i?e.setAttribute("media",i):e.removeAttribute("media"),r&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r))))," */")),e.styleSheet)e.styleSheet.cssText=o;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(o))}}var m=null,h=0;function v(e,n){var t,o,i;if(n.singleton){var r=h++;t=m||(m=d(n)),o=f.bind(null,t,r,!1),i=f.bind(null,t,r,!0)}else t=d(n),o=p.bind(null,t,n),i=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)};return o(e),function(n){if(n){if(n.css===e.css&&n.media===e.media&&n.sourceMap===e.sourceMap)return;o(e=n)}else i()}}e.exports=function(e,n){(n=n||{}).singleton||"boolean"==typeof n.singleton||(n.singleton=i());var t=s(e=e||[],n);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var o=0;o<t.length;o++){var i=c(t[o]);a[i].references--}for(var r=s(e,n),d=0;d<t.length;d++){var u=c(t[d]);0===a[u].references&&(a[u].updater(),a.splice(u,1))}t=r}}}},function(e,n,t){"use strict";(function(e){var t="object"==typeof e&&e&&e.Object===Object&&e;n.a=t}).call(this,t(5))},function(e,n){e.exports='<div class="com-verify-code"> <div class="com-verify-conent"> <div class="header">你好！请登录</div> <div class="com-verify-ipt"> <input id="J-muLogin-phone" type="tel" pattern="[0-9]*" class="phone" placeholder="请输入手机号" maxlength="11"/> </div> <div class="com-verify-ipt mt12"> <input id="J-muLogin-code" type="tel" pattern="[0-9]*" class="yzm" placeholder="请输入验证码" skip="true" key="code" maxlength="6"/> <div class="djs" id="J-muLogin-djs"></div> </div> <div class="text"> <p>1.未注册用户会自动创建驾考宝典账号</p> <p> 2.登录即同意 <a href="https://laofuzi.kakamobi.com/protocol/protocol.html?_productCategory=jiakaobaodian&_product=%E9%A9%BE%E8%80%83%E5%AE%9D%E5%85%B8&_appName=jiakaobaodian&protocolKey=jkbdUserAgreement">《用户使用协议》</a>和 <a href="https://laofuzi.kakamobi.com/protocol/protocol.html?_productCategory=jiakaobaodian&_product=%E9%A9%BE%E8%80%83%E5%AE%9D%E5%85%B8&_appName=jiakaobaodian&protocolKey=jkbdPrivateAgreement">《隐私政策》</a> </p> </div> <div class="com-verify-btn" id="J-muLogin-btn"> 确认 </div> </div> <div class="df-close" id="J-muLogin-close"></div> </div>'},function(e,n,t){"use strict";e.exports=function(e){var n=[];return n.toString=function(){return this.map((function(n){var t=function(e,n){var t=e[1]||"",o=e[3];if(!o)return t;if(n&&"function"==typeof btoa){var i=(a=o,c=btoa(unescape(encodeURIComponent(JSON.stringify(a)))),s="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(c),"/*# ".concat(s," */")),r=o.sources.map((function(e){return"/*# sourceURL=".concat(o.sourceRoot||"").concat(e," */")}));return[t].concat(r).concat([i]).join("\n")}var a,c,s;return[t].join("\n")}(n,e);return n[2]?"@media ".concat(n[2]," {").concat(t,"}"):t})).join("")},n.i=function(e,t,o){"string"==typeof e&&(e=[[null,e,""]]);var i={};if(o)for(var r=0;r<this.length;r++){var a=this[r][0];null!=a&&(i[a]=!0)}for(var c=0;c<e.length;c++){var s=[].concat(e[c]);o&&i[s[0]]||(t&&(s[2]?s[2]="".concat(t," and ").concat(s[2]):s[2]=t),n.push(s))}},n}},function(e,n){var t;t=function(){return this}();try{t=t||new Function("return this")()}catch(e){"object"==typeof window&&(t=window)}e.exports=t},function(e,n,t){"use strict";function o(e,n,t){var o=document.createElement("div");return document.getElementById("mcToast")&&document.body.removeChild(document.getElementById("mcToast")),o.innerText=e,o.setAttribute("id","mcToast"),o.style.position="fixed",o.style.left="50%",o.style.top="50%",o.style.transform="translate(-50%, -50%)",o.style.webkitTransform="translate(-50%, -50%)",o.style.background="rgba(0, 0, 0, 0.7)",o.style.zIndex="9999",o.style.padding="10px 20px",o.style.borderRadius="6px",o.style.textAlign="center",o.style.color="#ffffff",o.style.maxWidth="90%",o.style.minWidth="60%",o.style.fontSize="14px",o.style.lineHeight="1.5",n=n||2e3,document.body.appendChild(o),new Promise((function(e){setTimeout((function(){o.remove(),t&&t(),e()}),n)}))}function i(e){var n,t,o=Math.abs(parseInt((new Date).getTime()*Math.random()*1e4)).toString(),i=0;for(n=0;o.length>n;n++)i+=parseInt(o[n]);return t=[],i=function(e,n){return n-""+e.length<=0?e:(t[n]||(t[n]=Array(n+1).join(0)))+e}(i+=o.length,3-i.toString().length),e.toString()+o+i}function r(e){var n=[];for(var t in e)n.push(t+"="+encodeURIComponent(e[t]));return n.join("&")}function a(e,n){var t,o={};return n||(n=window.location.href),n.replace(/[#|?&]+([^=#|&]+)=([^#|&]*)/gi,(function(n,i,r){t=new RegExp(e,"gi"),o[i]=decodeURIComponent(r),e&&!i.match(t)&&delete o[i]})),o}t.r(n),t.d(n,"default",(function(){return ne}));var c=a();function s(){var e,n=localStorage.getItem("_appUser");return n||(n=(e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)})()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e(),localStorage.setItem("_appUser",n)),n}function d(e){var n=e.url,t=e.method,o=e.params;if(!n)return Promise.reject();var d={_r:i(1),_appUser:c._appUser||s()};return"GET"===t&&(d=Object.assign(o,d)),n=function(e,n){var t=a(null,e);return t=Object.assign(t,n),(e=e.substring(0,e.indexOf("?")>0?e.indexOf("?"):e.length))+"?"+r(t)}(n,d),t=(t||"get").toUpperCase(),o=o||{},new Promise((function(e,i){var a=new XMLHttpRequest;a.open(t,n),a.onreadystatechange=function(){if(4===a.readyState){var n=a.response;"string"==typeof n&&(n=JSON.parse(n)),a.status<400?n.success?e(n):i(n):a.status>=400&&i(n)}},"POST"===t?(a.setRequestHeader("content-type","application/x-www-form-urlencoded; charset=UTF-8"),a.send(r(o))):a.send()}))}function u(e){return(new DOMParser).parseFromString(e,"text/html").body.childNodes[0]}function l(e,n){var t=e.className?e.className.split(" "):[];-1===t.indexOf(n)&&(t.push(n),e.className=t.join(" "))}function f(e,n){var t=e.className?e.className.split(" "):[],o=t.indexOf(n);-1!==o&&(t.splice(o,1),e.className=t.join(" "))}var p=t(1),m=t.n(p),h=t(0),v=t.n(h),g={insert:"head",singleton:!1},y=(m()(v.a,g),v.a.locals,/\s/);var b=function(e){for(var n=e.length;n--&&y.test(e.charAt(n)););return n},x=/^\s+/;var j=function(e){return e?e.slice(0,b(e)+1).replace(x,""):e};var w=function(e){var n=typeof e;return null!=e&&("object"==n||"function"==n)},E=t(2),A="object"==typeof self&&self&&self.Object===Object&&self,k=(E.a||A||Function("return this")()).Symbol,C=Object.prototype,S=C.hasOwnProperty,I=C.toString,F=k?k.toStringTag:void 0;var N=function(e){var n=S.call(e,F),t=e[F];try{e[F]=void 0;var o=!0}catch(e){}var i=I.call(e);return o&&(n?e[F]=t:delete e[F]),i},O=Object.prototype.toString;var L=function(e){return O.call(e)},M=k?k.toStringTag:void 0;var T=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":M&&M in Object(e)?N(e):L(e)};var U=function(e){return null!=e&&"object"==typeof e};var D=function(e){return"symbol"==typeof e||U(e)&&"[object Symbol]"==T(e)},P=/^[-+]0x[0-9a-f]+$/i,J=/^0b[01]+$/i,B=/^0o[0-7]+$/i,R=parseInt;var _=function(e){if("number"==typeof e)return e;if(D(e))return NaN;if(w(e)){var n="function"==typeof e.valueOf?e.valueOf():e;e=w(n)?n+"":n}if("string"!=typeof e)return 0===e?e:+e;e=j(e);var t=J.test(e);return t||B.test(e)?R(e.slice(2),t?2:8):P.test(e)?NaN:+e};var z=function(e){return e?(e=_(e))===1/0||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0};var H=function(e){var n=z(e),t=n%1;return n==n?t?n-t:n:0};var V=function(e,n){var t;if("function"!=typeof n)throw new TypeError("Expected a function");return e=H(e),function(){return--e>0&&(t=n.apply(this,arguments)),e<=1&&(n=void 0),t}};var Q=function(e){return V(2,e)}((function(){return new Promise((function(e){var n,t;(n="https://cstaticdun.126.net/load.min.js?t="+(new Date).getTime(),t=document.createElement("script"),t.type="text/javascript",t.src=n,t.async=!1,document.head.appendChild(t),new Promise((function(e){t.onload=function(){e()}}))).then((function(){e()}))}))}));var W;function K(){W=u('<div class="mark126"><div class="captcha" id="captcha"></div></div>'),document.body.appendChild(W);var e=document.getElementById("captcha");return new Promise((function(n){Q().then((function(){window.initNECaptcha({captchaId:"6f92317b6e7d4f4faa77a360d65826c5",element:e,mode:"embed",width:"320px",onVerify:function(e,t){setTimeout((function(){e||void 0===t||(W&&W.parentNode&&W.parentNode.removeChild(W),n(t.validate))}),600)}},(function(e){console.log(e,"instance")}),(function(e){console.log(e,"errer")}))}))}))}var Y,q=t(3),G=t.n(q),$="mucang_authToken",Z="mucang_userInfo";function X(e){e=e||{},this.djs=-1,this.smsId=null,this.isDisabled=!1,this.callback=e.callback,this.$el=u(G.a),document.body.appendChild(this.$el),this.registerEvent(),this.djsControl()}function ee(){window.scroll(0,0)}function ne(e){new X(e)}X.prototype.onDjs=function(){var e=this;this.isDisabled||(/(1[3-9]\d|999)\d{8}/.test(this.getPhone())?K().then((function(n){e.sendSms(n)})):o("手机号码格式不正确"))},X.prototype.sendSms=function(e){var n,t=this;e&&(n={NECaptchaValidate:e,phoneNumber:this.getPhone()},d({url:"https://auth.kakamobi.com/api/web/v3/login-sms/check.htm",params:Object.assign(n,{_appName:"jiakaobaodian",_platform:"web",_authVersion:"1.5"}),method:"post"}).then((function(e){return e.data.smsId}))).then((function(e){t.smsId=e,t.onDjs2()}))},X.prototype.onDjs2=function(){var e=this;this.isDisabled=!0,this.djs=60,this.djsControl(),Y=setInterval((function(){var n=e.djs;--n>=0?e.djs=n:(e.isDisabled=!1,clearInterval(Y)),e.djsControl()}),1e3)},X.prototype.onSubmit=function(){var e,n=this,t=this.smsId,i=this.getPhone(),r=this.getCode();i&&r&&(/(1[3-9]\d|999)\d{8}/.test(i)?t?r?(e={phoneNumber:i,smsCode:r,smsId:t},d({url:"https://auth.kakamobi.com/api/web/v3/login-sms/login.htm",params:Object.assign(e,{_appName:"jiakaobaodian",_platform:"web",_authVersion:"1.5"}),method:"post"}).then((function(e){var n=e.data;return localStorage.setItem(Z,JSON.stringify(n)),localStorage.setItem($,n.authToken),n}))).then((function(){n.callback&&n.callback(),n.destroy()})).catch((function(e){o(e&&e.message||"登录校验失败，请重试")})):o("请填写验证码"):o("请先发送验证码"):o("手机号码格式不正确"))},X.prototype.getPhone=function(){return document.getElementById("J-muLogin-phone").value},X.prototype.getCode=function(){return document.getElementById("J-muLogin-code").value},X.prototype.destroy=function(){clearInterval(Y),this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},X.prototype.djsControl=function(){var e=-1===this.djs?"获取验证码":0===this.djs?"重新获取":"重新发送("+this.djs+")",n=document.getElementById("J-muLogin-djs");n.innerHTML=e,this.isDisabled?l(n,"disabeld"):f(n,"disabeld")},X.prototype.btnControl=function(){var e=this.getCode(),n=document.getElementById("J-muLogin-btn");e?l(n,"activied"):f(n,"activied")},X.prototype.registerEvent=function(){document.getElementById("J-muLogin-djs").addEventListener("click",this.onDjs.bind(this)),document.getElementById("J-muLogin-btn").addEventListener("click",this.onSubmit.bind(this)),document.getElementById("J-muLogin-close").addEventListener("click",this.destroy.bind(this)),document.getElementById("J-muLogin-phone").addEventListener("blur",ee),document.getElementById("J-muLogin-code").addEventListener("blur",ee),document.getElementById("J-muLogin-code").addEventListener("input",this.btnControl.bind(this))}}]).default}));