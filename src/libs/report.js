/* eslint-disable */
!(function (window) {
    var curLocation = window.location;
    var UASTR = window.navigator.userAgent || '';
    var isInApp = (!!(UASTR.toLowerCase().match(/mucang/g) || window.MCProtocolIosExecute || window.getMucangIOSWebViewData)) && !UASTR.toLowerCase().match(/\.mucang\./g);

    var Report = function (oortCfg) {
        this.oortCfg = oortCfg || {}
    };

    // 获取url参数
    Report.prototype.getURLParams = function (keyFilter) {
        var params = {}

        curLocation.href.replace(/[?&]([^=#]+)=([^&#]*)/gi, function (m, key, value) {
            if (value === 'null' || value === '(null)' || value === 'undefined') {
                value = ''
            }

            params[key] = decodeURIComponent(value)

            if (keyFilter && key && key.indexOf(keyFilter) === -1) {
                delete params[key]
            }
        })

        return params
    }

    // 获取系统平台
    Report.prototype.getPlatform = function () {
        var platform = ''

        if (window.navigator.userAgent.indexOf('Android') !== -1) {
            platform = 'Android'
        } else if (window.navigator.userAgent.indexOf('iPhone') !== -1) {
            platform = 'iPhone'
        } else if (window.navigator.userAgent.indexOf('Windows') !== -1) {
            platform = 'Windows'
        } else if (window.navigator.userAgent.indexOf('iPad') !== -1) {
            platform = 'iPad'
        } else {
            platform = 'Other'
        }

        return platform.toLocaleLowerCase()
    }

    // 检测或新建用户唯一id
    Report.prototype.setAppUser = function (name) {
        var urlParams = this.getURLParams('_') || {}
        var urlValue = urlParams[name] || ''
        var localName = ['MCH5Perf', name].join('_')
        var appUser = window.localStorage.getItem(localName)

        if (!appUser || urlValue) {
            appUser = urlValue || this.genUId()
            window.localStorage.setItem(localName, appUser)
        }

        return appUser
    }

    // H5签名
    Report.prototype.sign = function (a) {
        var c = Math.abs(parseInt(new Date().getTime() * Math.random() * 10000))
            .toString();
        var d = 0;
        var b;
        var e;

        for (b = 0;
            b < c.length;
            b++) {
            d += parseInt(c[b]);
        }
        e = (function (f) {
            return function (g, h) {
                return ((h - '' + g.length) <= 0) ? g : (f[h] || (f[h] = Array(h + 1)
                    .join(0))) + g;
            };
        })([]);
        d += c.length;
        d = e(d, 3 - d.toString().length);

        return a.toString() + c + d;
    }

    // 对象转url参数
    Report.prototype.object2param = function (obj) {
        var arr = []

        for (var key in obj) {
            if (key) {
                arr.push(key + '=' + encodeURIComponent(obj[key]))
            }
        }
        return arr.join('&')
    }

    // 发送数据
    Report.prototype.send = function (data) {
        var that = this
        var params
        var urlParams = that.getURLParams('_') || {}
        var urlParamsAll = that.getURLParams() || {};
        var localAppBaseParams = JSON.parse(localStorage.getItem('MC_G_ABP') || '{}') || {}
        var dataToSend = []

        var platform = that.getPlatform() || ''

        var name = 'debug'

        if (!urlParams._deviceId && localAppBaseParams._deviceId) {
            urlParams = localAppBaseParams
        }

        // 删除会影响请求的基础参数
        delete urlParams._r
        delete urlParams._v

        var atk = that.oortCfg.authToken || urlParamsAll.authToken || '';

        urlParams._platform = platform
        urlParams._appName = that.oortCfg.appName || urlParams._appName || ''
        urlParams._appUser = urlParams._appUser || window.localStorage.getItem('_appUser') || that.setAppUser('_appUser')
        urlParams._deviceId = urlParams._deviceId || that.setAppUser('_deviceId')
        urlParams._productCategory = that.oortCfg.productCategory || urlParams._productCategory
        urlParams._product = that.oortCfg.product || urlParams._product
        urlParams._r = that.sign(1)

        if (atk) {
            urlParams.authToken = atk;
        }

        params = that.object2param(urlParams)

        var pageName = '公共页'
        var fragmentName1 = 'h5页面'
        var actionType = '触发'
        var actionName = '异常上报'
        dataToSend = {
            group: name,
            event: [pageName, fragmentName1, actionType + actionName].join('_'),
            timestamp: new Date().getTime(),
            properties: {
                strs: Object.assign(data || {}, {
                    ua: UASTR,
                    pageUrl: curLocation.href
                })
            }
        }

        // app 内通过 app 发请求
        if (isInApp) {
            return that.sendByApp(dataToSend);
        }

        that.ajax({
            method: 'post',
            data: JSON.stringify([dataToSend]),
            url: 'https://oort-shipper.kakamobi.cn/api/h5/receiver/send.htm?' + params,
            dataType: 'json',
            async: true,
            success: function (ret) { },
            error: function () { }
        })
    }

    // 执行 app 协议
    Report.prototype.execApp = function (group, modules, method, userParams) {
        try {
            var GetSystem = function () {
                if (
                    isInApp ||
                    window.MCProtocolIosExecute ||
                    window.MCProtocolAndroidExecute ||
                    window.mcAndroidWebview1 ||
                    window.getMucangIOSWebViewData
                ) {
                    if (navigator.userAgent.indexOf('iPhone') > -1 || navigator.userAgent.indexOf('iPad') > -1) {
                        return 'iphone';
                    }
                    if (navigator.userAgent.indexOf('Android') > -1) {
                        return 'android';
                    }
                    return 'ipad';
                }
                return 'other';
            };
            var BuildUrl = function (group, modules, method) {
                if (modules) {
                    return 'https://' + group.toLowerCase() + '.luban.mucang.cn/' + modules.toLowerCase() + '/' + method;
                }
                return 'https://' + group.toLowerCase() + '.luban.mucang.cn/' + method;
            };
            var ObjectToParams = function (params) {
                var paramString = [];
                var e;
                var paramsType;
                for (e in params) {
                    paramsType = typeof params[e];
                    if (paramsType === 'string' || paramsType === 'number') {
                        paramString.push(e + '=' + encodeURIComponent(params[e]));
                    } else {
                        paramString.push(e + '=' + encodeURIComponent(JSON.stringify(params[e])));
                    }
                }
                return paramString;
            };
            var ParseParams = function (params) {
                var paramString = [];
                var e;
                if (params) {
                    for (e in params) {
                        switch (e) {
                            case 'config':
                                paramString = paramString.concat(ObjectToParams(params[e]));
                                break;
                            case 'callback':
                            case 'callbackName':
                                break;
                            default:
                                if (typeof params[e] === 'object') {
                                    paramString.push(e + '=' + encodeURIComponent(JSON.stringify(params[e])));
                                } else {
                                    paramString.push(e + '=' + encodeURIComponent(params[e]));
                                }
                        }
                    }
                    return paramString.join('&');
                }
                return '';
            };
            var GetCallbackName = function (params) {
                var e;
                if (params) {
                    for (e in params) {
                        if (e === 'callbackName') {
                            return params[e];
                        }
                    }
                }
                return null;
            };
            var GetCallback = function (params) {
                var e;
                if (params) {
                    for (e in params) {
                        if (e === 'callback') {
                            return params[e];
                        }
                    }
                }
                return null;
            };
            var IosExecute = function (url, params, callback, callbackName) {
                if (window.MCProtocolIosExecute) {
                    return window.MCProtocolIosExecute(url + (params ? '?' + params : ''), BuildCallback(callback, callbackName));
                }
                return window.getMucangIOSWebViewData(url + (params ? '?' + params : ''), BuildCallback(callback, callbackName));
            };
            var AndroidExecute = function (url, params, callback, callbackName) {
                // 木仓小程序兼容木仓协议，但是木仓协议不能运行小程序事件
                if (window.MCProtocolAndroidExecute) {
                    return window.MCProtocolAndroidExecute.exec(url + (params ? '?' + params : ''), BuildCallback(callback, callbackName) || 'NULL');
                }
                return window.mcAndroidWebview1 && window.mcAndroidWebview1.getMucangWebViewData(url + (params ? '?' + params : ''), BuildCallback(callback, callbackName) || 'NULL');
            };
            var BuildCallback = function (callback, callbackName) {
                return null;
            };

            var url = BuildUrl(group, modules, method, userParams);
            var params = ParseParams(userParams);
            var callback = GetCallback(userParams);
            var callbackName = GetCallbackName(userParams);

            switch (GetSystem()) {
                case 'iphone':
                    return IosExecute(url, params, callback, callbackName);
                case 'android':
                    return AndroidExecute(url, params, callback, callbackName);
                default:
                    BuildCallback(callback, callbackName);
            }
        } catch (error) {
            console.log('execApp err', error);
        }
    }

    // 通过 app 发送打点数据
    Report.prototype.sendByApp = function (data) {
        var that = this;

        that.execApp('Core', 'System', 'stat', {
            eventId: data.group,
            eventName: data.event,
            eventLabel: data.event,
            properties: data.properties,
            needCommon: data.needCommon || 1
        })
    }

    Report.prototype.ajax = function (obj) {
        var xhr = new XMLHttpRequest()

        function callback() {
            if (xhr.status === 200) {
                try {
                    obj.success(JSON.parse(xhr.responseText))
                } catch (e) {
                    console.log(e)
                }
            } else {
                obj.error(xhr.status, xhr.statusText)
            }
        }

        if (typeof obj.data === 'object' && obj.contentType !== false) {
            obj.data = this.object2param(obj.data)
        }

        if (obj.method === 'get') {
            obj.url = obj.url.indexOf('?') === -1 ? obj.url + '?' + obj.data : obj.url + '&' + obj.data;
        }
        if (obj.async === true) {
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    callback()
                }
            }
        }

        xhr.open(obj.method, obj.url, true)
        xhr.withCredentials = true
        if (obj.contentType !== false) {
            xhr.setRequestHeader('Content-Type', obj.contentType || 'application/x-www-form-urlencoded')
        }

        if (obj.method === 'post') {
            xhr.send(obj.data)
        } else {
            xhr.send(null)
        }

        if (obj.async === false) {
            callback()
        }
    }
    window.Report = new Report({
        appName: 'jiakaoshouke',
        productCategory: 'jiakaoshouke',
        product: '驾考授课工具',
        authToken: JSON.parse(localStorage.getItem('mucang_userInfo') || '{}').authToken || '',
    })
})(window);