{"name": "vite-project", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --force", "build": "vue-tsc -b && vite build", "build:test": "vue-tsc -b && npx vite build --mode development", "preview": "vite preview"}, "dependencies": {"@jiakaobaodian/jiakaobaodian-utils": "^1.1.8", "@mdi/font": "^7.0.96", "@simplex/simple-base": "6.4.9", "@simplex/simple-base-sso": "3.0.2", "@simplex/simple-core": "4.0.22", "@simplex/simple-mcprotocol": "3.6.1", "@simplex/simple-mock": "1.0.1", "@simplex/simple-oort": "4.3.9", "lodash-es": "^4.17.21", "material-design-icons-iconfont": "^6.7.0", "mitt": "^3.0.0", "pinia": "^2.0.13", "tim-js-sdk": "^2.22.1", "trtc-sdk-v5": "^5.8.0", "vconsole": "^3.15.1", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuetify": "^3.6.13"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@rollup/plugin-commonjs": "^26.0.1", "@types/node": "^22.7.5", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "^5.1.2", "rollup": "4.21.3", "sass": "^1.50.0", "terser": "^5.39.0", "typescript": "^5.2.2", "vite": "5.3.1", "vite-plugin-static-copy": "^2.2.0", "vite-plugin-vconsole": "^2.1.1", "vite-plugin-vuetify": "^2.0.3", "vue-tsc": "2.0.21"}}