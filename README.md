# 驾考私教在线授课工具

## 项目背景
驾考私教业务需要一套完善的在线教学系统，以满足以下核心需求：
- **实时音视频互动**: 讲师与学员能够进行实时音视频通话，模拟线下教学场景
- **互动白板**: 讲师可以通过白板进行板书、演示课件、以及做题等操作，学员可以实时查看并参与随堂练习
- **课程管理**: 讲师可以管理课程内容、布置课后作业、发起随堂测验等
- **学员信息管理**: 讲师可以查看学员的学习进度、练习情况、考试结果等

## 项目概述
本项目是驾考私教业务的在线一对一教学系统的**授课工具**部分，支持讲师与学员之间的实时互动教学。系统通过集成腾讯云的实时音视频（TRTC）、即时通信（IM）和互动白板技术，实现了高效、稳定的在线教学体验。

### 核心特性
- 🎥 **多端适配**: 支持PC端（讲师）和移动端（学员）
- 🎨 **互动白板**: 集成腾讯云白板，支持多种绘图工具
- 💬 **实时通信**: 基于腾讯云IM的即时消息和音视频通话
- 📊 **学员档案**: 实时查看学员学习进度和考试情况
- 📝 **随堂测验**: 支持发起测验并实时查看结果
- 🔧 **设备管理**: 音视频设备检测和切换

## 术语定义

| 术语 | 说明 |
|------|------|
| **授课工具** | 本项目，一对一教学系统的核心组件，提供即时通信+互动白板+学员信息展示等功能 |
| **电子教案** | 配套的教案管理系统，提供做题+课件页面（供讲师嵌入互动白板进行授课）+课程管理 |
| **白板推流** | 基于腾讯云的在线白板技术，通过TRTC+IM+白板三种SDK协同工作，IM同步白板操作到云端，云端录制白板内容并通过TRTC实时推送给学员 |

## 部署信息

| 环境 | 地址 | 说明 |
|------|------|------|
| **授课工具** | https://laofuzi.kakamobi.com/personal-training-live/ | 本项目部署地址 |
| **电子教案** | https://laofuzi.kakamobi.com/personal-training-management/ | 配套教案系统 |
| **代码仓库** | https://git.mucang.cn/jiakaobaodian-webfront/personal-training-management | Git仓库地址 |

## 技术栈

### 核心框架
- **Vue 3.2.13** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript超集
- **Vite 5.3.1** - 现代化构建工具
- **Vuetify 3.6.13** - Material Design组件库
- **Pinia 2.0.13** - Vue状态管理库
- **Vue Router 4.0.3** - 官方路由管理器

### 第三方SDK
- **腾讯云TRTC SDK v5.8.0** - 实时音视频通信
- **腾讯云IM SDK v2.22.1** - 即时通信
- **腾讯云互动白板** - 在线白板功能
- **VConsole 3.15.1** - 移动端调试工具

### 开发工具
- **Sass** - CSS预处理器
- **ESLint + TypeScript** - 代码质量检查
- **Vite插件生态** - 构建优化和开发体验

## 运行环境
- **PC端**: 讲师使用，支持现代浏览器
- **移动端**: 学员使用，支持驾考宝典APP内嵌浏览器
- **微信浏览器**: 学员备用访问方式

## 功能特性

### 🎯 核心功能

#### 1. 学员档案管理
- **基础信息**: 学员个人资料、学历、年龄、考试城市等
- **错题本**: 专项/知识点错题、近十场考试错题、模拟考试错题
- **考试记录**: 模拟考试记录、总体学习进度
- **多维度查看**: 支持车型科目切换，全面了解学员学习状况

#### 2. 实时互动教学
- **音视频通话**: 基于腾讯云TRTC的高质量音视频通信
- **互动白板**: 支持多种绘图工具的实时协作白板
- **屏幕共享**: 支持课件演示和内容分享
- **设备管理**: 摄像头、麦克风、扬声器设备检测和切换

#### 3. 随堂测验系统
- **即时发起**: 讲师可随时发起测验，学员端实时弹窗
- **实时统计**: 自动统计答题情况（答对/答错题数）
- **结果分析**: 生成测验结果，支持针对性讲解
- **历史记录**: 保存测验历史，便于回顾

#### 4. 用户与权限管理
- **角色区分**:
  - 讲师（PC端）: 完整教学功能
  - 学员（移动端）: 学习和互动功能
  - 督导: 监督和管理功能
- **成员管理**: 查看课堂成员，支持禁言、禁音等操作
- **权限控制**: 基于角色的功能权限分配

#### 5. 通信与交互
- **即时消息**: 基于腾讯云IM的实时聊天
- **快捷回复**: 学员端预设快捷回复，提高互动效率
- **消息管理**: 支持消息历史查看和管理

### 🎨 白板工具
- **绘图工具**: 画笔、矩形、文本等多种绘图工具
- **操作工具**: 鼠标选择、橡皮擦、一键清空
- **协作功能**: 实时同步，多人协作
- **内容管理**: 支持保存和恢复白板内容

### 📚 教案集成
#### 错题资源
- 专项/知识点错题
- 近十场考试错题
- 单次模拟考试错题
- 所有试题库
- 专项试题
- 随堂测验结果

#### 课件资源
- 专项课件
- 考前冲刺课件
- 自定义教学内容

## 项目架构

### 目录结构
```
personal-training-live/
├── 📁 src/                       # 源代码目录
│   ├── 📁 components/            # Vue组件
│   │   ├── 📁 Board/             # 🎨 白板相关组件
│   │   │   ├── Main.vue          # 白板主组件
│   │   │   ├── Sketch.vue        # 白板绘制组件
│   │   │   ├── Toolbar.vue       # 白板工具栏
│   │   │   └── Bottombar.vue     # 白板底部栏
│   │   ├── 📁 Main/              # 🏠 主页面组件
│   │   │   ├── MainPC.vue        # PC端主页面
│   │   │   ├── MainH5.vue        # 移动端主页面
│   │   │   └── MainWX.vue        # 微信端主页面
│   │   ├── 📁 MessageList/       # 💬 消息列表组件
│   │   ├── 📁 ChatEditor/        # ✏️ 聊天编辑器
│   │   ├── 📁 TUI/               # 🎛️ 腾讯云UI组件
│   │   ├── StudentDetail.vue     # 👨‍🎓 学员档案组件
│   │   ├── TeachPlan.vue         # 📚 教案组件
│   │   ├── ExamDialog.vue        # 📝 考试弹窗
│   │   ├── ExamResultDialog.vue  # 📊 考试结果弹窗
│   │   ├── Player.vue            # 🎥 音视频播放器
│   │   ├── Device.vue            # 🔧 设备管理
│   │   └── ...                   # 其他功能组件
│   ├── 📁 services/              # 🔌 核心服务
│   │   ├── trtc.ts               # TRTC音视频服务
│   │   ├── tim.ts                # IM即时通信服务
│   │   └── board.ts              # 白板服务
│   ├── 📁 store/                 # 🗄️ 状态管理 (Pinia)
│   │   ├── basic.ts              # 基础信息状态
│   │   ├── room.ts               # 房间状态管理
│   │   ├── board.ts              # 白板状态管理
│   │   └── chat.ts               # 聊天状态管理
│   ├── 📁 hooks/                 # 🪝 自定义Hooks
│   │   ├── useAuth.ts            # 认证逻辑
│   │   ├── useDeviceManager.ts   # 设备管理
│   │   └── useMitt.ts            # 事件总线
│   ├── 📁 router/                # 🛣️ 路由配置
│   ├── 📁 utils/                 # 🛠️ 工具函数
│   ├── 📁 types/                 # 📝 TypeScript类型定义
│   ├── 📁 views/                 # 📄 页面视图
│   └── main.ts                   # 🚀 应用入口
├── 📁 static/                    # 静态资源
├── 📄 package.json               # 项目配置
├── 📄 vite.config.mts            # Vite构建配置
├── 📄 .gitlab-ci.yml             # CI/CD配置
└── 📄 README.md                  # 项目文档
```

### 核心架构设计

#### 🏗️ 分层架构
```
┌─────────────────────────────────────┐
│           视图层 (Views)              │
│     MainPC / MainH5 / MainWX        │
├─────────────────────────────────────┤
│          组件层 (Components)         │
│   Board / Chat / Student / Exam     │
├─────────────────────────────────────┤
│         状态管理层 (Store)            │
│    Basic / Room / Board / Chat      │
├─────────────────────────────────────┤
│          服务层 (Services)           │
│      TRTC / TIM / Board APIs        │
├─────────────────────────────────────┤
│         第三方SDK层 (SDKs)            │
│   腾讯云TRTC / IM / 互动白板         │
└─────────────────────────────────────┘
```

## 系统架构与数据流

### 🔄 核心数据流
```mermaid
graph TB
    A[用户认证] --> B[房间初始化]
    B --> C[SDK初始化]
    C --> D[TRTC连接]
    C --> E[IM连接]
    C --> F[白板连接]

    D --> G[音视频流]
    E --> H[即时消息]
    F --> I[白板数据]

    G --> J[实时互动]
    H --> J
    I --> J

    J --> K[教学场景]
    K --> L[学员档案]
    K --> M[随堂测验]
    K --> N[教案展示]
```

### 🎯 用户角色与权限

| 角色 | 权限 | 使用端 | 主要功能 |
|------|------|--------|----------|
| **讲师** | 完整教学权限 | PC端 | 白板操作、发起测验、查看学员档案、音视频控制 |
| **学员** | 学习互动权限 | 移动端 | 观看白板、参与测验、音视频互动、聊天回复 |
| **督导** | 监督管理权限 | PC/移动端 | 课堂监督、质量管控 |

### 🔌 第三方SDK集成

#### 腾讯云TRTC (实时音视频)
- **版本**: trtc-sdk-v5@5.8.0
- **功能**: 音视频通话、屏幕共享、设备管理
- **配置**: 支持自动接收音频、音量检测、网络质量监控

#### 腾讯云IM (即时通信)
- **版本**: tim-js-sdk@2.22.1
- **功能**: 群组管理、消息收发、用户状态同步
- **特性**: 支持禁言、用户资料管理、消息历史

#### 腾讯云互动白板
- **版本**: TEduBoard 2.9.6
- **功能**: 实时协作白板、文件展示、绘图工具
- **特性**: 数据同步、历史回放、多种绘图工具

### 📱 多端适配策略

#### PC端 (讲师)
- **组件**: MainPC.vue
- **特性**: 完整功能界面、白板工具栏、设备管理面板
- **布局**: 左侧白板区域 + 右侧功能面板

#### 移动端 (学员)
- **组件**: MainH5.vue
- **特性**: 触屏优化、简化界面、快捷操作
- **布局**: 全屏白板 + 浮动控制面板

#### 微信端 (学员备用)
- **组件**: MainWX.vue
- **特性**: 微信环境适配、引导下载APP
- **功能**: 基础观看功能

## 开发指南

### 🚀 快速开始

#### 环境要求
- Node.js >= 16.14.0
- npm >= 8.0.0

#### 安装依赖
```bash
npm install
```

#### 开发运行
```bash
npm run dev
```
访问: http://localhost:5556

#### 构建部署
```bash
# 生产环境构建
npm run build

# 测试环境构建
npm run build:test

# 开发环境构建
npm run build:dev
```

### 🔧 配置说明

#### Vite配置 (vite.config.mts)
- **端口**: 5556
- **代理**: 支持开发环境API代理
- **插件**: Vuetify、VConsole、静态资源复制
- **兼容性**: 支持Chrome 52+、Android 4.1+、iOS 7.1+

#### 环境变量
- `DEV`: 开发环境标识
- `BUILD_ART_PATH`: 构建输出目录 (dist)

### 📋 开发规范

#### 代码结构
- **组件**: 使用Vue 3 Composition API
- **状态管理**: Pinia stores
- **类型安全**: TypeScript严格模式
- **样式**: Sass + Vuetify主题

#### 命名规范
- **组件**: PascalCase (如: StudentDetail.vue)
- **文件**: kebab-case (如: use-auth.ts)
- **变量**: camelCase
- **常量**: UPPER_SNAKE_CASE

## API接口

### 🔐 认证相关
- `POST /api/web/teaching-room/auth.htm` - 获取用户签名
- `GET /api/web/teaching-room/get-teaching-room-info.htm` - 获取房间信息
- `POST /api/web/teaching-room/create-teaching-room.htm` - 创建教学房间

### 📚 课程相关
- `GET /api/web/course/get-course.htm` - 获取课程信息

### 👨‍🎓 学员相关
- `GET /api/web/student/get-student-summary.htm` - 获取学员学习概况
- `GET /api/web/student/get-student-profile.htm` - 获取学员基础信息

### 📝 考试相关
- 支持获取错题、考试记录等相关接口
- 集成随堂测验功能接口

## 故障排除

### 🔧 常见问题

#### 1. SDK初始化失败
**问题**: 白板或音视频SDK初始化失败
**解决方案**:
- 检查网络连接
- 确认SDK版本兼容性
- 查看控制台错误日志
- 验证appId和userSig是否正确

#### 2. 音视频设备问题
**问题**: 摄像头或麦克风无法使用
**解决方案**:
- 检查浏览器权限设置
- 确认设备未被其他应用占用
- 尝试刷新页面重新授权
- 检查设备驱动是否正常

#### 3. 白板同步问题
**问题**: 白板内容不同步
**解决方案**:
- 检查IM连接状态
- 确认用户权限配置
- 重新初始化白板SDK
- 检查网络稳定性

#### 4. 移动端兼容性
**问题**: 移动端功能异常
**解决方案**:
- 检查浏览器版本支持
- 确认触屏事件处理
- 验证响应式布局
- 测试不同设备分辨率

### 📊 性能优化

#### 前端优化
- **代码分割**: 使用Vite动态导入
- **资源压缩**: Terser压缩JavaScript
- **缓存策略**: 合理设置静态资源缓存
- **懒加载**: 组件和路由懒加载

#### SDK优化
- **日志级别**: 生产环境设置合适的日志级别
- **连接复用**: 避免重复初始化SDK
- **内存管理**: 及时清理不用的资源
- **网络优化**: 合理设置重连策略

## 部署说明

### 🚀 CI/CD流程

#### GitLab CI配置
```yaml
# .gitlab-ci.yml
image: hub.mucang.cn/mc-public/node:16.14.0

variables:
  BUILD_ART_PATH: dist
  BUILD_CMD: |-
    master: npm run build
    dev*: npm run build:dev
    test*: npm run build:test
```

#### 分支策略
- **master**: 生产环境自动部署
- **dev**: 开发环境自动部署
- **test**: 测试环境自动部署

### 🌐 环境配置

#### 生产环境
- **域名**: https://laofuzi.kakamobi.com/personal-training-live/
- **CDN**: 静态资源CDN加速
- **监控**: 错误监控和性能监控

#### 开发环境
- **本地开发**: http://localhost:5556
- **热更新**: Vite HMR支持
- **调试工具**: VConsole移动端调试

## 项目的逻辑视图

1、**实时互动**

![](https://jiakao-web.mc-cdn.cn/jiakao-web/2025/03/28/20/1e2b527b44ca435787e9a9366ee60422.png)